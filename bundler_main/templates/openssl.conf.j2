{# j2lint: disable=single-statement-per-line #}
[ openssl_init ]
providers = provider_sect
config_diagnostics = 1

[ provider_sect ]
fips = fips_sect
default = default_sect

[ req ]
default_bits       = 4096
default_md         = sha512
default_keyfile = {{ dns_name }}.tls.key
distinguished_name = req_distinguished_name
x509_extensions = v3_ca
prompt = no

[ req_distinguished_name ]
countryName                     = US
stateOrProvinceName             = Pennsylvania
localityName                    = Valley Forge
0.organizationName              = Lockheed Martin
commonName                      = {{ dns_name }}

[ v3_ca ]
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid:always,issuer
basicConstraints = critical, CA:true
keyUsage = critical, digitalSignature
nameConstraints = critical,permitted;DNS:{{ dns_name }},permitted;DNS:*.{{ dns_name }}{% for alt_name in alt_names %},permitted;DNS:{{ alt_name }}{% endfor %}

subjectAltName = @alt_names

[alt_names]
DNS.1 = {{ dns_name }}
DNS.2 = *.{{ dns_name }}
{% set dns_number = namespace(index=3) %}
{% for alt_name in alt_names %}
DNS.{{ dns_number.index }} = {{ alt_name }}
{%     set dns_number.index = dns_number.index + 1 %}
{% endfor %}
