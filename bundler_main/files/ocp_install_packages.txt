aardvark-dns-[0-9]+.*.rpm
abattis-cantarell-fonts-[0-9]+.*.rpm
acl-[0-9]+.*.rpm
adwaita-cursor-theme-[0-9]+.*.rpm
adwaita-icon-theme-[0-9]+.*.rpm
aide-[0-9]+.*.rpm
alsa-lib-[0-9]+.*.rpm
alternatives-[0-9]+.*.rpm
amazon-libdnf-plugin-[0-9]+.*.rpm
ansible-collection-redhat-satellite_operations-[0-9]+.*.rpm
ansible-collection-redhat-satellite-[0-9]+.*.rpm
ansible-core-[0-9]+.*.rpm
ansible-runner-[0-9]+.*.rpm
ansible-test-[0-9]+.*.rpm
ansiblerole-foreman_scap_client-[0-9]+.*.rpm
ansiblerole-insights-client-[0-9]+.*.rpm
apr-[0-9]+.*.rpm
apr-util-[0-9]+.*.rpm
apr-util-bdb-[0-9]+.*.rpm
apr-util-openssl-[0-9]+.*.rpm
at-spi2-atk-[0-9]+.*.rpm
at-spi2-core-[0-9]+.*.rpm
atk-[0-9]+.*.rpm
attr-[0-9]+.*.rpm
audispd-plugins-[0-9]+.*.rpm
audit-[0-9]+.*.rpm
audit-libs-[0-9]+.*.rpm
augeas-libs-[0-9]+.*.rpm
authselect-[0-9]+.*.rpm
authselect-compat-[0-9]+.*.rpm
authselect-libs-[0-9]+.*.rpm
autogen-libopts-[0-9]+.*.rpm
avahi-glib-[0-9]+.*.rpm
avahi-libs-[0-9]+.*.rpm
basesystem-[0-9]+.*.rpm
bash-[0-9]+.*.rpm
bind-libs-[0-9]+.*.rpm
bind-license-[0-9]+.*.rpm
bind-utils-[0-9]+.*.rpm
binutils-[0-9]+.*.rpm
binutils-gold-[0-9]+.*.rpm
bluez-libs-[0-9]+.*.rpm
boost-atomic-[0-9]+.*.rpm
boost-chrono-[0-9]+.*.rpm
boost-date-time-[0-9]+.*.rpm
boost-iostreams-[0-9]+.*.rpm
boost-program-options-[0-9]+.*.rpm
boost-random-[0-9]+.*.rpm
boost-regex-[0-9]+.*.rpm
boost-system-[0-9]+.*.rpm
boost-thread-[0-9]+.*.rpm
bubblewrap-[0-9]+.*.rpm
bzip2-[0-9]+.*.rpm
bzip2-libs-[0-9]+.*.rpm
c-ares-[0-9]+.*.rpm
ca-certificates-[0-9]+.*.rpm
cairo-[0-9]+.*.rpm
cairo-gobject-[0-9]+.*.rpm
candlepin-[0-9]+.*.rpm
candlepin-selinux-[0-9]+.*.rpm
capstone-[0-9]+.*.rpm
celt051-[0-9]+.*.rpm
checkpolicy-[0-9]+.*.rpm
chkconfig-[0-9]+.*.rpm
chrony-[0-9]+.*.rpm
clevis-[0-9]+.*.rpm
clevis-dracut-[0-9]+.*.rpm
clevis-luks-[0-9]+.*.rpm
clevis-systemd-[0-9]+.*.rpm
cloud-init-[0-9]+.*.rpm
cloud-utils-growpart-[0-9]+.*.rpm
colord-libs-[0-9]+.*.rpm
composefs-libs-[0-9]+.*.rpm
conmon-[0-9]+.*.rpm
container-selinux-[0-9]+.*.rpm
containers-common-[0-9]+.*.rpm
copy-jdk-configs-[0-9]+.*.rpm
coreutils-[0-9]+.*.rpm
coreutils-common-[0-9]+.*.rpm
cpio-[0-9]+.*.rpm
cracklib-[0-9]+.*.rpm
cracklib-dicts-[0-9]+.*.rpm
createrepo_c-libs-[0-9]+.*.rpm
criu-[0-9]+.*.rpm
criu-libs-[0-9]+.*.rpm
cronie-[0-9]+.*.rpm
cronie-anacron-[0-9]+.*.rpm
crontabs-[0-9]+.*.rpm
crun-[0-9]+.*.rpm
crypto-policies-[0-9]+.*.rpm
crypto-policies-scripts-[0-9]+.*.rpm
cryptsetup-[0-9]+.*.rpm
cryptsetup-libs-[0-9]+.*.rpm
cups-libs-[0-9]+.*.rpm
curl-[0-9]+.*.rpm
cyrus-sasl-[0-9]+.*.rpm
cyrus-sasl-gssapi-[0-9]+.*.rpm
cyrus-sasl-lib-[0-9]+.*.rpm
daxctl-libs-[0-9]+.*.rpm
dbus-[0-9]+.*.rpm
dbus-broker-[0-9]+.*.rpm
dbus-common-[0-9]+.*.rpm
dbus-libs-[0-9]+.*.rpm
dbus-tools-[0-9]+.*.rpm
dconf-[0-9]+.*.rpm
debugedit-[0-9]+.*.rpm
dejavu-fonts-common-[0-9]+.*.rpm
dejavu-sans-fonts-[0-9]+.*.rpm
dejavu-sans-mono-fonts-[0-9]+.*.rpm
device-mapper-[0-9]+.*.rpm
device-mapper-event-[0-9]+.*.rpm
device-mapper-event-libs-[0-9]+.*.rpm
device-mapper-libs-[0-9]+.*.rpm
device-mapper-multipath-libs-[0-9]+.*.rpm
device-mapper-persistent-data-[0-9]+.*.rpm
dhcp-client-[0-9]+.*.rpm
dhcp-common-[0-9]+.*.rpm
diffutils-[0-9]+.*.rpm
dmidecode-[0-9]+.*.rpm
dnf-[0-9]+.*.rpm
dnf-data-[0-9]+.*.rpm
dnf-plugins-core-[0-9]+.*.rpm
dnsmasq-[0-9]+.*.rpm
dosfstools-[0-9]+.*.rpm
dracut-[0-9]+.*.rpm
dracut-config-generic-[0-9]+.*.rpm
dracut-config-rescue-[0-9]+.*.rpm
dracut-network-[0-9]+.*.rpm
dracut-squash-[0-9]+.*.rpm
dwz-[0-9]+.*.rpm
dynflow-utils-[0-9]+.*.rpm
e2fsprogs-[0-9]+.*.rpm
e2fsprogs-libs-[0-9]+.*.rpm
ecj-[0-9]+.*.rpm
ed-[0-9]+.*.rpm
edk2-ovmf-[0-9]+.*.rpm
efi-filesystem-[0-9]+.*.rpm
efi-srpm-macros-[0-9]+.*.rpm
efibootmgr-[0-9]+.*.rpm
efivar-libs-[0-9]+.*.rpm
elfutils-[0-9]+.*.rpm
elfutils-debuginfod-client-[0-9]+.*.rpm
elfutils-default-yama-scope-[0-9]+.*.rpm
elfutils-libelf-[0-9]+.*.rpm
elfutils-libs-[0-9]+.*.rpm
esc-[0-9]+.*.rpm
ethtool-[0-9]+.*.rpm
exempi-[0-9]+.*.rpm
exiv2-[0-9]+.*.rpm
exiv2-libs-[0-9]+.*.rpm
expat-[0-9]+.*.rpm
fapolicyd-[0-9]+.*.rpm
fapolicyd-selinux-[0-9]+.*.rpm
fdk-aac-free-[0-9]+.*.rpm
file-[0-9]+.*.rpm
file-libs-[0-9]+.*.rpm
filesystem-[0-9]+.*.rpm
findutils-[0-9]+.*.rpm
firewalld-[0-9]+.*.rpm
firewalld-filesystem-[0-9]+.*.rpm
flac-libs-[0-9]+.*.rpm
flashrom-[0-9]+.*.rpm
flatpak-[0-9]+.*.rpm
flatpak-selinux-[0-9]+.*.rpm
flatpak-session-helper-[0-9]+.*.rpm
fontconfig-[0-9]+.*.rpm
fontpackages-filesystem-[0-9]+.*.rpm
fonts-filesystem-[0-9]+.*.rpm
fonts-srpm-macros-[0-9]+.*.rpm
foreman-[0-9]+.*.rpm
foreman-bootloaders-redhat-[0-9]+.*.rpm
foreman-bootloaders-redhat-tftpboot-[0-9]+.*.rpm
foreman-cli-[0-9]+.*.rpm
foreman-debug-[0-9]+.*.rpm
foreman-dynflow-sidekiq-[0-9]+.*.rpm
foreman-ec2-[0-9]+.*.rpm
foreman-fapolicyd-[0-9]+.*.rpm
foreman-installer-[0-9]+.*.rpm
foreman-installer-katello-[0-9]+.*.rpm
foreman-libvirt-[0-9]+.*.rpm
foreman-openstack-[0-9]+.*.rpm
foreman-ovirt-[0-9]+.*.rpm
foreman-postgresql-[0-9]+.*.rpm
foreman-proxy-[0-9]+.*.rpm
foreman-proxy-fapolicyd-[0-9]+.*.rpm
foreman-redis-[0-9]+.*.rpm
foreman-selinux-[0-9]+.*.rpm
foreman-service-[0-9]+.*.rpm
foreman-vmware-[0-9]+.*.rpm
freetype-[0-9]+.*.rpm
fribidi-[0-9]+.*.rpm
fstrm-[0-9]+.*.rpm
fuse-[0-9]+.*.rpm
fuse-common-[0-9]+.*.rpm
fuse-libs-[0-9]+.*.rpm
fuse-overlayfs-[0-9]+.*.rpm
fuse3-[0-9]+.*.rpm
fuse3-libs-[0-9]+.*.rpm
fwupd-[0-9]+.*.rpm
fwupd-plugin-flashrom-[0-9]+.*.rpm
gawk-[0-9]+.*.rpm
gawk-all-langpacks-[0-9]+.*.rpm
gdb-minimal-[0-9]+.*.rpm
gdbm-libs-[0-9]+.*.rpm
gdisk-[0-9]+.*.rpm
gdk-pixbuf2-[0-9]+.*.rpm
gdk-pixbuf2-modules-[0-9]+.*.rpm
geoclue2-[0-9]+.*.rpm
geolite2-city-[0-9]+.*.rpm
geolite2-country-[0-9]+.*.rpm
gettext-[0-9]+.*.rpm
gettext-libs-[0-9]+.*.rpm
ghc-srpm-macros-[0-9]+.*.rpm
giflib-[0-9]+.*.rpm
git-core-[0-9]+.*.rpm
gjs-[0-9]+.*.rpm
glib-networking-[0-9]+.*.rpm
glib2-[0-9]+.*.rpm
glibc-[0-9]+.*.rpm
glibc-common-[0-9]+.*.rpm
glibc-gconv-extra-[0-9]+.*.rpm
glibc-langpack-en-[0-9]+.*.rpm
glibc-minimal-langpack-[0-9]+.*.rpm
glusterfs-[0-9]+.*.rpm
glusterfs-api-[0-9]+.*.rpm
glusterfs-cli-[0-9]+.*.rpm
glusterfs-client-xlators-[0-9]+.*.rpm
glusterfs-libs-[0-9]+.*.rpm
gmp-[0-9]+.*.rpm
gnupg2-[0-9]+.*.rpm
gnutls-[0-9]+.*.rpm
gnutls-dane-[0-9]+.*.rpm
gnutls-utils-[0-9]+.*.rpm
go-srpm-macros-[0-9]+.*.rpm
gobject-introspection-[0-9]+.*.rpm
gpgme-[0-9]+.*.rpm
gpm-libs-[0-9]+.*.rpm
graphene-[0-9]+.*.rpm
graphite2-[0-9]+.*.rpm
grep-[0-9]+.*.rpm
groff-base-[0-9]+.*.rpm
grub2-common-[0-9]+.*.rpm
grub2-efi-x64-[0-9]+.*.rpm
grub2-efi-x64-modules-[0-9]+.*.rpm
grub2-pc-[0-9]+.*.rpm
grub2-pc-modules-[0-9]+.*.rpm
grub2-tools-[0-9]+.*.rpm
grub2-tools-extra-[0-9]+.*.rpm
grub2-tools-minimal-[0-9]+.*.rpm
grubby-[0-9]+.*.rpm
gsettings-desktop-schemas-[0-9]+.*.rpm
gsm-[0-9]+.*.rpm
gssproxy-[0-9]+.*.rpm
gstreamer1-[0-9]+.*.rpm
gstreamer1-plugins-base-[0-9]+.*.rpm
gtk-update-icon-cache-[0-9]+.*.rpm
gtk3-[0-9]+.*.rpm
gzip-[0-9]+.*.rpm
harfbuzz-[0-9]+.*.rpm
hdparm-[0-9]+.*.rpm
hicolor-icon-theme-[0-9]+.*.rpm
hostname-[0-9]+.*.rpm
httpd-[0-9]+.*.rpm
httpd-core-[0-9]+.*.rpm
httpd-filesystem-[0-9]+.*.rpm
httpd-tools-[0-9]+.*.rpm
hwdata-[0-9]+.*.rpm
ima-evm-utils-[0-9]+.*.rpm
info-[0-9]+.*.rpm
inih-[0-9]+.*.rpm
initscripts-[0-9]+.*.rpm
initscripts-rename-device-[0-9]+.*.rpm
initscripts-service-[0-9]+.*.rpm
insights-client-[0-9]+.*.rpm
ipcalc-[0-9]+.*.rpm
ipmitool-[0-9]+.*.rpm
iproute-[0-9]+.*.rpm
iproute-tc-[0-9]+.*.rpm
ipset-[0-9]+.*.rpm
ipset-libs-[0-9]+.*.rpm
iptables-[0-9]+.*.rpm
iptables-ebtables-[0-9]+.*.rpm
iptables-libs-[0-9]+.*.rpm
iptables-nft-[0-9]+.*.rpm
iputils-[0-9]+.*.rpm
ipxe-bootimgs-x86-[0-9]+.*.rpm
ipxe-roms-qemu-[0-9]+.*.rpm
irqbalance-[0-9]+.*.rpm
iscsi-initiator-utils-[0-9]+.*.rpm
iscsi-initiator-utils-iscsiuio-[0-9]+.*.rpm
isns-utils-libs-[0-9]+.*.rpm
iso-codes-[0-9]+.*.rpm
iwl100-firmware-[0-9]+.*.rpm
iwl1000-firmware-[0-9]+.*.rpm
iwl105-firmware-[0-9]+.*.rpm
iwl135-firmware-[0-9]+.*.rpm
iwl2000-firmware-[0-9]+.*.rpm
iwl2030-firmware-[0-9]+.*.rpm
iwl3160-firmware-[0-9]+.*.rpm
iwl5000-firmware-[0-9]+.*.rpm
iwl5150-firmware-[0-9]+.*.rpm
iwl6000g2a-firmware-[0-9]+.*.rpm
iwl6050-firmware-[0-9]+.*.rpm
iwl7260-firmware-[0-9]+.*.rpm
jansson-[0-9]+.*.rpm
jasper-libs-[0-9]+.*.rpm
java-[0-9]+.*.rpm
javapackages-filesystem-[0-9]+.*.rpm
javapackages-tools-[0-9]+.*.rpm
jbigkit-libs-[0-9]+.*.rpm
jitterentropy-[0-9]+.*.rpm
jose-[0-9]+.*.rpm
jq-[0-9]+.*.rpm
json-c-[0-9]+.*.rpm
json-glib-[0-9]+.*.rpm
katello-[0-9]+.*.rpm
katello-certs-tools-[0-9]+.*.rpm
katello-client-bootstrap-[0-9]+.*.rpm
katello-common-[0-9]+.*.rpm
katello-debug-[0-9]+.*.rpm
katello-selinux-[0-9]+.*.rpm
kbd-[0-9]+.*.rpm
kbd-legacy-[0-9]+.*.rpm
kbd-misc-[0-9]+.*.rpm
keepalived-[0-9]+.*.rpm
kernel-[0-9]+.*.rpm
kernel-core-[0-9]+.*.rpm
kernel-modules-[0-9]+.*.rpm
kernel-modules-core-[0-9]+.*.rpm
kernel-srpm-macros-[0-9]+.*.rpm
kernel-tools-[0-9]+.*.rpm
kernel-tools-libs-[0-9]+.*.rpm
kexec-tools-[0-9]+.*.rpm
keyutils-[0-9]+.*.rpm
keyutils-libs-[0-9]+.*.rpm
kmod-[0-9]+.*.rpm
kmod-libs-[0-9]+.*.rpm
kpartx-[0-9]+.*.rpm
krb5-libs-[0-9]+.*.rpm
langpacks-core-en-[0-9]+.*.rpm
langpacks-core-font-en-[0-9]+.*.rpm
langpacks-en-[0-9]+.*.rpm
lcms2-[0-9]+.*.rpm
ldns-[0-9]+.*.rpm
less-[0-9]+.*.rpm
libacl-[0-9]+.*.rpm
libaio-[0-9]+.*.rpm
libarchive-[0-9]+.*.rpm
libassuan-[0-9]+.*.rpm
libasyncns-[0-9]+.*.rpm
libatasmart-[0-9]+.*.rpm
libattr-[0-9]+.*.rpm
libbasicobjects-[0-9]+.*.rpm
libblkid-[0-9]+.*.rpm
libblkio-[0-9]+.*.rpm
libblockdev-[0-9]+.*.rpm
libblockdev-crypto-[0-9]+.*.rpm
libblockdev-fs-[0-9]+.*.rpm
libblockdev-loop-[0-9]+.*.rpm
libblockdev-mdraid-[0-9]+.*.rpm
libblockdev-part-[0-9]+.*.rpm
libblockdev-swap-[0-9]+.*.rpm
libblockdev-utils-[0-9]+.*.rpm
libbpf-[0-9]+.*.rpm
libbrotli-[0-9]+.*.rpm
libburn-[0-9]+.*.rpm
libbytesize-[0-9]+.*.rpm
libcanberra-[0-9]+.*.rpm
libcap-[0-9]+.*.rpm
libcap-ng-[0-9]+.*.rpm
libcap-ng-python3-[0-9]+.*.rpm
libcbor-[0-9]+.*.rpm
libcollection-[0-9]+.*.rpm
libcom_err-[0-9]+.*.rpm
libcomps-[0-9]+.*.rpm
libcurl-[0-9]+.*.rpm
libdaemon-[0-9]+.*.rpm
libdatrie-[0-9]+.*.rpm
libdb-[0-9]+.*.rpm
libdhash-[0-9]+.*.rpm
libdnf-[0-9]+.*.rpm
libdnf-plugin-subscription-manager-[0-9]+.*.rpm
libdrm-[0-9]+.*.rpm
libeconf-[0-9]+.*.rpm
libedit-[0-9]+.*.rpm
libepoxy-[0-9]+.*.rpm
libestr-[0-9]+.*.rpm
libev-[0-9]+.*.rpm
libevent-[0-9]+.*.rpm
libexif-[0-9]+.*.rpm
libfastjson-[0-9]+.*.rpm
libfdisk-[0-9]+.*.rpm
libfdt-[0-9]+.*.rpm
libffi-[0-9]+.*.rpm
libfido2-[0-9]+.*.rpm
libfontenc-[0-9]+.*.rpm
libgcc-[0-9]+.*.rpm
libgcrypt-[0-9]+.*.rpm
libgexiv2-[0-9]+.*.rpm
libglvnd-[0-9]+.*.rpm
libglvnd-egl-[0-9]+.*.rpm
libglvnd-gles-[0-9]+.*.rpm
libglvnd-glx-[0-9]+.*.rpm
libgomp-[0-9]+.*.rpm
libgpg-error-[0-9]+.*.rpm
libgsf-[0-9]+.*.rpm
libgudev-[0-9]+.*.rpm
libgusb-[0-9]+.*.rpm
libgxps-[0-9]+.*.rpm
libibverbs-[0-9]+.*.rpm
libicu-[0-9]+.*.rpm
libidn2-[0-9]+.*.rpm
libini_config-[0-9]+.*.rpm
libiptcdata-[0-9]+.*.rpm
libiscsi-[0-9]+.*.rpm
libisoburn-[0-9]+.*.rpm
libisofs-[0-9]+.*.rpm
libjcat-[0-9]+.*.rpm
libjose-[0-9]+.*.rpm
libjpeg-turbo-[0-9]+.*.rpm
libkadm5-[0-9]+.*.rpm
libkcapi-[0-9]+.*.rpm
libkcapi-hmaccalc-[0-9]+.*.rpm
libksba-[0-9]+.*.rpm
libldac-[0-9]+.*.rpm
libldb-[0-9]+.*.rpm
libluksmeta-[0-9]+.*.rpm
libmaxminddb-[0-9]+.*.rpm
libmnl-[0-9]+.*.rpm
libmodman-[0-9]+.*.rpm
libmodulemd-[0-9]+.*.rpm
libmount-[0-9]+.*.rpm
libnbd-[0-9]+.*.rpm
libndp-[0-9]+.*.rpm
libnet-[0-9]+.*.rpm
libnetfilter_conntrack-[0-9]+.*.rpm
libnfnetlink-[0-9]+.*.rpm
libnfsidmap-[0-9]+.*.rpm
libnftnl-[0-9]+.*.rpm
libnghttp2-[0-9]+.*.rpm
libnl3-[0-9]+.*.rpm
libnl3-cli-[0-9]+.*.rpm
libnotify-[0-9]+.*.rpm
libnsl2-[0-9]+.*.rpm
libnvme-[0-9]+.*.rpm
libogg-[0-9]+.*.rpm
libpath_utils-[0-9]+.*.rpm
libpcap-[0-9]+.*.rpm
libpciaccess-[0-9]+.*.rpm
libpipeline-[0-9]+.*.rpm
libpkgconf-[0-9]+.*.rpm
libpmem-[0-9]+.*.rpm
libpng-[0-9]+.*.rpm
libpq-[0-9]+.*.rpm
libproxy-[0-9]+.*.rpm
libpsl-[0-9]+.*.rpm
libpwquality-[0-9]+.*.rpm
libqb-[0-9]+.*.rpm
librados2-[0-9]+.*.rpm
librbd1-[0-9]+.*.rpm
librdmacm-[0-9]+.*.rpm
libref_array-[0-9]+.*.rpm
librepo-[0-9]+.*.rpm
libreport-filesystem-[0-9]+.*.rpm
libreswan-[0-9]+.*.rpm
librhsm-[0-9]+.*.rpm
librsvg2-[0-9]+.*.rpm
libsbc-[0-9]+.*.rpm
libseccomp-[0-9]+.*.rpm
libselinux-[0-9]+.*.rpm
libselinux-utils-[0-9]+.*.rpm
libsemanage-[0-9]+.*.rpm
libsepol-[0-9]+.*.rpm
libsigsegv-[0-9]+.*.rpm
libslirp-[0-9]+.*.rpm
libsmartcols-[0-9]+.*.rpm
libsndfile-[0-9]+.*.rpm
libsolv-[0-9]+.*.rpm
libsoup-[0-9]+.*.rpm
libss-[0-9]+.*.rpm
libssh-[0-9]+.*.rpm
libssh-config-[0-9]+.*.rpm
libsss_.*map-[0-9]+.*.rpm
libsss_certmap-[0-9]+.*.rpm
libsss_idmap-[0-9]+.*.rpm
libsss_nss_idmap-[0-9]+.*.rpm
libsss_sudo-[0-9]+.*.rpm
libstdc++-[0-9]+.*.rpm
libsysfs-[0-9]+.*.rpm
libtalloc-[0-9]+.*.rpm
libtasn1-[0-9]+.*.rpm
libtdb-[0-9]+.*.rpm
libteam-[0-9]+.*.rpm
libtevent-[0-9]+.*.rpm
libthai-[0-9]+.*.rpm
libtheora-[0-9]+.*.rpm
libtiff-[0-9]+.*.rpm
libtirpc-[0-9]+.*.rpm
libtool-ltdl-[0-9]+.*.rpm
libtpms-[0-9]+.*.rpm
libtraceevent-[0-9]+.*.rpm
libtracker-sparql-[0-9]+.*.rpm
libudisks2-[0-9]+.*.rpm
libunistring-[0-9]+.*.rpm
liburing-[0-9]+.*.rpm
libusbx-[0-9]+.*.rpm
libuser-[0-9]+.*.rpm
libutempter-[0-9]+.*.rpm
libuuid-[0-9]+.*.rpm
libuv-[0-9]+.*.rpm
libverto-[0-9]+.*.rpm
libverto-libev-[0-9]+.*.rpm
libverto-libevent-[0-9]+.*.rpm
libvirt-[0-9]+.*.rpm
libvirt-client-[0-9]+.*.rpm
libvirt-client-qemu-[0-9]+.*.rpm
libvirt-daemon-[0-9]+.*.rpm
libvirt-daemon-common-[0-9]+.*.rpm
libvirt-daemon-config-network-[0-9]+.*.rpm
libvirt-daemon-config-nwfilter-[0-9]+.*.rpm
libvirt-daemon-driver-interface-[0-9]+.*.rpm
libvirt-daemon-driver-network-[0-9]+.*.rpm
libvirt-daemon-driver-nodedev-[0-9]+.*.rpm
libvirt-daemon-driver-nwfilter-[0-9]+.*.rpm
libvirt-daemon-driver-qemu-[0-9]+.*.rpm
libvirt-daemon-driver-secret-[0-9]+.*.rpm
libvirt-daemon-driver-storage-[0-9]+.*.rpm
libvirt-daemon-driver-storage-core-[0-9]+.*.rpm
libvirt-daemon-driver-storage-disk-[0-9]+.*.rpm
libvirt-daemon-driver-storage-gluster-[0-9]+.*.rpm
libvirt-daemon-driver-storage-iscsi-[0-9]+.*.rpm
libvirt-daemon-driver-storage-iscsi-direct-[0-9]+.*.rpm
libvirt-daemon-driver-storage-logical-[0-9]+.*.rpm
libvirt-daemon-driver-storage-mpath-[0-9]+.*.rpm
libvirt-daemon-driver-storage-rbd-[0-9]+.*.rpm
libvirt-daemon-driver-storage-scsi-[0-9]+.*.rpm
libvirt-daemon-lock-[0-9]+.*.rpm
libvirt-daemon-log-[0-9]+.*.rpm
libvirt-daemon-plugin-lockd-[0-9]+.*.rpm
libvirt-daemon-proxy-[0-9]+.*.rpm
libvirt-libs-[0-9]+.*.rpm
libvisual-[0-9]+.*.rpm
libvorbis-[0-9]+.*.rpm
libwayland-client-[0-9]+.*.rpm
libwayland-cursor-[0-9]+.*.rpm
libwayland-egl-[0-9]+.*.rpm
libwayland-server-[0-9]+.*.rpm
libwebp-[0-9]+.*.rpm
libX11-[0-9]+.*.rpm
libX11-common-[0-9]+.*.rpm
libX11-xcb-[0-9]+.*.rpm
libXau-[0-9]+.*.rpm
libxcb-[0-9]+.*.rpm
libXcomposite-[0-9]+.*.rpm
libxcrypt-[0-9]+.*.rpm
libxcrypt-compat-[0-9]+.*.rpm
libXcursor-[0-9]+.*.rpm
libXdamage-[0-9]+.*.rpm
libXext-[0-9]+.*.rpm
libXfixes-[0-9]+.*.rpm
libXft-[0-9]+.*.rpm
libXi-[0-9]+.*.rpm
libXinerama-[0-9]+.*.rpm
libxkbcommon-[0-9]+.*.rpm
libxml2-[0-9]+.*.rpm
libxmlb-[0-9]+.*.rpm
libXrandr-[0-9]+.*.rpm
libXrender-[0-9]+.*.rpm
libxshmfence-[0-9]+.*.rpm
libxslt-[0-9]+.*.rpm
libXtst-[0-9]+.*.rpm
libXv-[0-9]+.*.rpm
libXxf86vm-[0-9]+.*.rpm
libyaml-[0-9]+.*.rpm
libzstd-[0-9]+.*.rpm
linux-firmware-[0-9]+.*.rpm
linux-firmware-whence-[0-9]+.*.rpm
lksctp-tools-[0-9]+.*.rpm
llvm-libs-[0-9]+.*.rpm
lm_sensors-libs-[0-9]+.*.rpm
lmdb-libs-[0-9]+.*.rpm
logrotate-[0-9]+.*.rpm
low-memory-monitor-[0-9]+.*.rpm
lshw-[0-9]+.*.rpm
lshw-B.02.19.2-[0-9]+.*.rpm
lsof-[0-9]+.*.rpm
lsscsi-[0-9]+.*.rpm
lua-[0-9]+.*.rpm
lua-libs-[0-9]+.*.rpm
lua-posix-[0-9]+.*.rpm
lua-srpm-macros-[0-9]+.*.rpm
luksmeta-[0-9]+.*.rpm
lvm2-[0-9]+.*.rpm
lvm2-libs-[0-9]+.*.rpm
lz4-libs-[0-9]+.*.rpm
lzo-[0-9]+.*.rpm
lzop-[0-9]+.*.rpm
mailcap-[0-9]+.*.rpm
man-db-[0-9]+.*.rpm
mariadb-connector-c-[0-9]+.*.rpm
mariadb-connector-c-config-[0-9]+.*.rpm
mdadm-[0-9]+.*.rpm
mdevctl-[0-9]+.*.rpm
mesa-dri-drivers-[0-9]+.*.rpm
mesa-filesystem-[0-9]+.*.rpm
mesa-libEGL-[0-9]+.*.rpm
mesa-libgbm-[0-9]+.*.rpm
mesa-libGL-[0-9]+.*.rpm
mesa-libglapi-[0-9]+.*.rpm
microcode_ctl-[0-9]+.*.rpm
mkfontscale-[0-9]+.*.rpm
mod_http2-[0-9]+.*.rpm
mod_lua-[0-9]+.*.rpm
mod_ssl-[0-9]+.*.rpm
ModemManager-glib-[0-9]+.*.rpm
mokutil-[0-9]+.*.rpm
mpdecimal-[0-9]+.*.rpm
mpfr-[0-9]+.*.rpm
mtools-[0-9]+.*.rpm
nbdkit-[0-9]+.*.rpm
nbdkit-basic-filters-[0-9]+.*.rpm
nbdkit-basic-plugins-[0-9]+.*.rpm
nbdkit-curl-plugin-[0-9]+.*.rpm
nbdkit-selinux-[0-9]+.*.rpm
nbdkit-server-[0-9]+.*.rpm
nbdkit-ssh-plugin-[0-9]+.*.rpm
ncurses-[0-9]+.*.rpm
ncurses-base-[0-9]+.*.rpm
ncurses-libs-[0-9]+.*.rpm
ndctl-libs-[0-9]+.*.rpm
net-snmp-agent-libs-[0-9]+.*.rpm
net-snmp-libs-[0-9]+.*.rpm
net-tools-[0-9]+.*.rpm
netavark-[0-9]+.*.rpm
netcf-libs-[0-9]+.*.rpm
nettle-[0-9]+.*.rpm
NetworkManager-[0-9]+.*.rpm
NetworkManager-cloud-setup-[0-9]+.*.rpm
NetworkManager-config-server-[0-9]+.*.rpm
NetworkManager-libnm-[0-9]+.*.rpm
NetworkManager-team-[0-9]+.*.rpm
NetworkManager-tui-[0-9]+.*.rpm
newt-[0-9]+.*.rpm
nfs-utils-[0-9]+.*.rpm
nftables-[0-9]+.*.rpm
nispor-[0-9]+.*.rpm
nmap-[0-9]+.*.rpm
nmap-ncat-[0-9]+.*.rpm
nmstate-[0-9]+.*.rpm
npth-[0-9]+.*.rpm
nspr-[0-9]+.*.rpm
nss-[0-9]+.*.rpm
nss-softokn-[0-9]+.*.rpm
nss-softokn-freebl-[0-9]+.*.rpm
nss-sysinit-[0-9]+.*.rpm
nss-tools-[0-9]+.*.rpm
nss-util-[0-9]+.*.rpm
numactl-libs-[0-9]+.*.rpm
numad-[0-9]+.*.rpm
nvme-cli-[0-9]+.*.rpm
ocaml-srpm-macros-[0-9]+.*.rpm
oddjob-[0-9]+.*.rpm
oddjob-mkhomedir-[0-9]+.*.rpm
oniguruma-[0-9]+.*.rpm
openblas-srpm-macros-[0-9]+.*.rpm
openjpeg2-[0-9]+.*.rpm
openldap-[0-9]+.*.rpm
opensc-[0-9]+.*.rpm
openscap-[0-9]+.*.rpm
openscap-[0-9]+.*.rpm
openscap-scanner-[0-9]+.*.rpm
openssh-[0-9]+.*.rpm
openssh-clients-[0-9]+.*.rpm
openssh-server-[0-9]+.*.rpm
openssl-[0-9]+.*.rpm
openssl-fips-provider-[0-9]+.*.rpm
openssl-libs-[0-9]+.*.rpm
openssl-pkcs11-[0-9]+.*.rpm
opus-[0-9]+.*.rpm
orc-[0-9]+.*.rpm
os-prober-[0-9]+.*.rpm
osinfo-db-[0-9]+.*.rpm
osinfo-db-tools-[0-9]+.*.rpm
ostree-libs-[0-9]+.*.rpm
p11-kit-[0-9]+.*.rpm
p11-kit-server-[0-9]+.*.rpm
p11-kit-trust-[0-9]+.*.rpm
pam-[0-9]+.*.rpm
pango-[0-9]+.*.rpm
parted-[0-9]+.*.rpm
passt-[0-9]+.*.rpm
passt-selinux-[0-9]+.*.rpm
passwd-[0-9]+.*.rpm
patch-[0-9]+.*.rpm
pciutils-[0-9]+.*.rpm
pciutils-libs-[0-9]+.*.rpm
pcre-[0-9]+.*.rpm
pcre2-[0-9]+.*.rpm
pcre2-syntax-[0-9]+.*.rpm
pcsc-lite-[0-9]+.*.rpm
pcsc-lite-ccid-[0-9]+.*.rpm
pcsc-lite-libs-[0-9]+.*.rpm
perl-AutoLoader-[0-9]+.*.rpm
perl-B-[0-9]+.*.rpm
perl-base-[0-9]+.*.rpm
perl-Carp-[0-9]+.*.rpm
perl-Class-Struct-[0-9]+.*.rpm
perl-constant-[0-9]+.*.rpm
perl-Data-Dumper-[0-9]+.*.rpm
perl-Digest-[0-9]+.*.rpm
perl-Digest-MD5-[0-9]+.*.rpm
perl-Encode-[0-9]+.*.rpm
perl-Errno-[0-9]+.*.rpm
perl-Exporter-[0-9]+.*.rpm
perl-Fcntl-[0-9]+.*.rpm
perl-File-Basename-[0-9]+.*.rpm
perl-File-Path-[0-9]+.*.rpm
perl-File-stat-[0-9]+.*.rpm
perl-File-Temp-[0-9]+.*.rpm
perl-FileHandle-[0-9]+.*.rpm
perl-Getopt-Long-[0-9]+.*.rpm
perl-Getopt-Std-[0-9]+.*.rpm
perl-HTTP-Tiny-[0-9]+.*.rpm
perl-if-[0-9]+.*.rpm
perl-interpreter-[0-9]+.*.rpm
perl-IO-[0-9]+.*.rpm
perl-IO-Socket-IP-[0-9]+.*.rpm
perl-IO-Socket-SSL-[0-9]+.*.rpm
perl-IPC-Open3-[0-9]+.*.rpm
perl-libnet-[0-9]+.*.rpm
perl-libs-[0-9]+.*.rpm
perl-macros-[0-9]+.*.rpm
perl-MIME-Base64-[0-9]+.*.rpm
perl-Mozilla-CA-[0-9]+.*.rpm
perl-mro-[0-9]+.*.rpm
perl-NDBM_File-[0-9]+.*.rpm
perl-Net-SSLeay-[0-9]+.*.rpm
perl-overload-[0-9]+.*.rpm
perl-overloading-[0-9]+.*.rpm
perl-parent-[0-9]+.*.rpm
perl-PathTools-[0-9]+.*.rpm
perl-Pod-Escapes-[0-9]+.*.rpm
perl-Pod-Perldoc-[0-9]+.*.rpm
perl-Pod-Simple-[0-9]+.*.rpm
perl-Pod-Usage-[0-9]+.*.rpm
perl-podlators-[0-9]+.*.rpm
perl-POSIX-[0-9]+.*.rpm
perl-Scalar-List-Utils-[0-9]+.*.rpm
perl-SelectSaver-[0-9]+.*.rpm
perl-Socket-[0-9]+.*.rpm
perl-srpm-macros-[0-9]+.*.rpm
perl-Storable-[0-9]+.*.rpm
perl-subs-[0-9]+.*.rpm
perl-Symbol-[0-9]+.*.rpm
perl-Term-ANSIColor-[0-9]+.*.rpm
perl-Term-Cap-[0-9]+.*.rpm
perl-Text-ParseWords-[0-9]+.*.rpm
perl-Text-Tabs\+Wrap-[0-9]+.*.rpm
perl-threads-[0-9]+.*.rpm
perl-threads-shared-[0-9]+.*.rpm
perl-Time-Local-[0-9]+.*.rpm
perl-Unicode-Normalize-[0-9]+.*.rpm
perl-URI-[0-9]+.*.rpm
perl-vars-[0-9]+.*.rpm
pigz-[0-9]+.*.rpm
pipewire-[0-9]+.*.rpm
pipewire-alsa-[0-9]+.*.rpm
pipewire-jack-audio-connection-kit-[0-9]+.*.rpm
pipewire-jack-audio-connection-kit-libs-[0-9]+.*.rpm
pipewire-libs-[0-9]+.*.rpm
pipewire-pulseaudio-[0-9]+.*.rpm
pixman-[0-9]+.*.rpm
pkgconf-[0-9]+.*.rpm
pkgconf-m4-[0-9]+.*.rpm
pkgconf-pkg-config-[0-9]+.*.rpm
podman-[0-9]+.*.rpm
policycoreutils-[0-9]+.*.rpm
policycoreutils-python-utils-[0-9]+.*.rpm
polkit-[0-9]+.*.rpm
polkit-libs-[0-9]+.*.rpm
polkit-pkla-compat-[0-9]+.*.rpm
poppler-[0-9]+.*.rpm
poppler-data-[0-9]+.*.rpm
poppler-glib-[0-9]+.*.rpm
popt-[0-9]+.*.rpm
postfix-[0-9]+.*.rpm
postgresql-[0-9]+.*.rpm
postgresql-contrib-[0-9]+.*.rpm
postgresql-evr-[0-9]+.*.rpm
postgresql-private-libs-[0-9]+.*.rpm
postgresql-server-[0-9]+.*.rpm
prefixdevname-[0-9]+.*.rpm
procps-ng-[0-9]+.*.rpm
protobuf-[0-9]+.*.rpm
protobuf-c-[0-9]+.*.rpm
psmisc-[0-9]+.*.rpm
publicsuffix-list-dafsa-[0-9]+.*.rpm
pulpcore-obsolete-packages-[0-9]+.*.rpm
pulpcore-selinux-[0-9]+.*.rpm
pulseaudio-libs-[0-9]+.*.rpm
puppet-agent-[0-9]+.*.rpm
puppet-agent-oauth-[0-9]+.*.rpm
pyproject-srpm-macros-[0-9]+.*.rpm
python-srpm-macros-[0-9]+.*.rpm
python-unversioned-command-[0-9]+.*.rpm
python3-[0-9]+.*.rpm
python3-attrs-[0-9]+.*.rpm
python3-audit-[0-9]+.*.rpm
python3-babel-[0-9]+.*.rpm
python3-cffi-[0-9]+.*.rpm
python3-chardet-[0-9]+.*.rpm
python3-cloud-what-[0-9]+.*.rpm
python3-configobj-[0-9]+.*.rpm
python3-cryptography-[0-9]+.*.rpm
python3-dateutil-[0-9]+.*.rpm
python3-dbus-[0-9]+.*.rpm
python3-decorator-[0-9]+.*.rpm
python3-distro-[0-9]+.*.rpm
python3-dnf-[0-9]+.*.rpm
python3-dnf-plugins-core-[0-9]+.*.rpm
python3-file-magic-[0-9]+.*.rpm
python3-firewall-[0-9]+.*.rpm
python3-gobject-base-[0-9]+.*.rpm
python3-gobject-base-noarch-[0-9]+.*.rpm
python3-gpg-[0-9]+.*.rpm
python3-hawkey-[0-9]+.*.rpm
python3-idna-[0-9]+.*.rpm
python3-iniparse-[0-9]+.*.rpm
python3-inotify-[0-9]+.*.rpm
python3-jinja2-[0-9]+.*.rpm
python3-jsonpatch-[0-9]+.*.rpm
python3-jsonpointer-[0-9]+.*.rpm
python3-jsonschema-[0-9]+.*.rpm
python3-libcomps-[0-9]+.*.rpm
python3-libdnf-[0-9]+.*.rpm
python3-libnmstate-[0-9]+.*.rpm
python3-librepo-[0-9]+.*.rpm
python3-libs-[0-9]+.*.rpm
python3-libselinux-[0-9]+.*.rpm
python3-libsemanage-[0-9]+.*.rpm
python3-libvirt-[0-9]+.*.rpm
python3-linux-procfs-[0-9]+.*.rpm
python3-lxml-[0-9]+.*.rpm
python3-markupsafe-[0-9]+.*.rpm
python3-netifaces-[0-9]+.*.rpm
python3-nftables-[0-9]+.*.rpm
python3-nispor-[0-9]+.*.rpm
python3-oauthlib-[0-9]+.*.rpm
python3-packaging-[0-9]+.*.rpm
python3-perf-[0-9]+.*.rpm
python3-pip-wheel-[0-9]+.*.rpm
python3-ply-[0-9]+.*.rpm
python3-policycoreutils-[0-9]+.*.rpm
python3-prettytable-[0-9]+.*.rpm
python3-pycparser-[0-9]+.*.rpm
python3-pyparsing-[0-9]+.*.rpm
python3-pyrsistent-[0-9]+.*.rpm
python3-pyserial-[0-9]+.*.rpm
python3-pysocks-[0-9]+.*.rpm
python3-pytz-[0-9]+.*.rpm
python3-pyudev-[0-9]+.*.rpm
python3-pyyaml-[0-9]+.*.rpm
python3-requests-[0-9]+.*.rpm
python3-resolvelib-[0-9]+.*.rpm
python3-rpm-[0-9]+.*.rpm
python3-setools-[0-9]+.*.rpm
python3-setuptools-[0-9]+.*.rpm
python3-setuptools-wheel-[0-9]+.*.rpm
python3-six-[0-9]+.*.rpm
python3-slip-[0-9]+.*.rpm
python3-slip-dbus-[0-9]+.*.rpm
python3-subscription-manager-rhsm-[0-9]+.*.rpm
python3-systemd-[0-9]+.*.rpm
python3-urllib3-[0-9]+.*.rpm
python3-varlink-[0-9]+.*.rpm
python3-websockify-[0-9]+.*.rpm
python3.11-[0-9]+.*.rpm
python3.11-aiodns-[0-9]+.*.rpm
python3.11-aiofiles-[0-9]+.*.rpm
python3.11-aiohttp-[0-9]+.*.rpm
python3.11-aiohttp-xmlrpc-[0-9]+.*.rpm
python3.11-aioredis-[0-9]+.*.rpm
python3.11-aiosignal-[0-9]+.*.rpm
python3.11-ansible-builder-[0-9]+.*.rpm
python3.11-ansible-runner-[0-9]+.*.rpm
python3.11-asgiref-[0-9]+.*.rpm
python3.11-async-lru-[0-9]+.*.rpm
python3.11-async-timeout-[0-9]+.*.rpm
python3.11-asyncio-throttle-[0-9]+.*.rpm
python3.11-attrs-[0-9]+.*.rpm
python3.11-backoff-[0-9]+.*.rpm
python3.11-bindep-[0-9]+.*.rpm
python3.11-bleach-[0-9]+.*.rpm
python3.11-bleach-allowlist-[0-9]+.*.rpm
python3.11-brotli-[0-9]+.*.rpm
python3.11-certifi-[0-9]+.*.rpm
python3.11-cffi-[0-9]+.*.rpm
python3.11-charset-normalizer-[0-9]+.*.rpm
python3.11-click-[0-9]+.*.rpm
python3.11-click-shell-[0-9]+.*.rpm
python3.11-contextlib2-[0-9]+.*.rpm
python3.11-createrepo_c-[0-9]+.*.rpm
python3.11-cryptography-[0-9]+.*.rpm
python3.11-daemon-[0-9]+.*.rpm
python3.11-dateutil-[0-9]+.*.rpm
python3.11-defusedxml-[0-9]+.*.rpm
python3.11-deprecated-[0-9]+.*.rpm
python3.11-diff-match-patch-[0-9]+.*.rpm
python3.11-distro-[0-9]+.*.rpm
python3.11-django-[0-9]+.*.rpm
python3.11-django-filter-[0-9]+.*.rpm
python3.11-django-guid-[0-9]+.*.rpm
python3.11-django-import-export-[0-9]+.*.rpm
python3.11-django-lifecycle-[0-9]+.*.rpm
python3.11-django-readonly-field-[0-9]+.*.rpm
python3.11-djangorestframework-[0-9]+.*.rpm
python3.11-djangorestframework-queryfields-[0-9]+.*.rpm
python3.11-docutils-[0-9]+.*.rpm
python3.11-drf-access-policy-[0-9]+.*.rpm
python3.11-drf-nested-routers-[0-9]+.*.rpm
python3.11-drf-spectacular-[0-9]+.*.rpm
python3.11-dynaconf-[0-9]+.*.rpm
python3.11-et-xmlfile-[0-9]+.*.rpm
python3.11-flake8-[0-9]+.*.rpm
python3.11-frozenlist-[0-9]+.*.rpm
python3.11-galaxy-importer-[0-9]+.*.rpm
python3.11-gitdb-[0-9]+.*.rpm
python3.11-gitpython-[0-9]+.*.rpm
python3.11-gnupg-[0-9]+.*.rpm
python3.11-googleapis-common-protos-[0-9]+.*.rpm
python3.11-grpcio-[0-9]+.*.rpm
python3.11-gunicorn-[0-9]+.*.rpm
python3.11-idna-[0-9]+.*.rpm
python3.11-importlib-metadata-[0-9]+.*.rpm
python3.11-inflection-[0-9]+.*.rpm
python3.11-iniparse-[0-9]+.*.rpm
python3.11-jinja2-[0-9]+.*.rpm
python3.11-jq-[0-9]+.*.rpm
python3.11-json_stream_rs_tokenizer-[0-9]+.*.rpm
python3.11-json_stream-[0-9]+.*.rpm
python3.11-jsonschema-[0-9]+.*.rpm
python3.11-libcomps-[0-9]+.*.rpm
python3.11-libs-[0-9]+.*.rpm
python3.11-lockfile-[0-9]+.*.rpm
python3.11-lxml-[0-9]+.*.rpm
python3.11-markdown-[0-9]+.*.rpm
python3.11-markuppy-[0-9]+.*.rpm
python3.11-markupsafe-[0-9]+.*.rpm
python3.11-mccabe-[0-9]+.*.rpm
python3.11-multidict-[0-9]+.*.rpm
python3.11-odfpy-[0-9]+.*.rpm
python3.11-openpyxl-[0-9]+.*.rpm
python3.11-opentelemetry_api-[0-9]+.*.rpm
python3.11-opentelemetry_distro_otlp-[0-9]+.*.rpm
python3.11-opentelemetry_distro-[0-9]+.*.rpm
python3.11-opentelemetry_exporter_otlp_proto_common-[0-9]+.*.rpm
python3.11-opentelemetry_exporter_otlp_proto_grpc-[0-9]+.*.rpm
python3.11-opentelemetry_exporter_otlp_proto_http-[0-9]+.*.rpm
python3.11-opentelemetry_exporter_otlp-[0-9]+.*.rpm
python3.11-opentelemetry_instrumentation_django-[0-9]+.*.rpm
python3.11-opentelemetry_instrumentation_wsgi-[0-9]+.*.rpm
python3.11-opentelemetry_instrumentation-[0-9]+.*.rpm
python3.11-opentelemetry_proto-[0-9]+.*.rpm
python3.11-opentelemetry_sdk-[0-9]+.*.rpm
python3.11-opentelemetry_semantic_conventions-[0-9]+.*.rpm
python3.11-opentelemetry_util_http-[0-9]+.*.rpm
python3.11-packaging-[0-9]+.*.rpm
python3.11-parsley-[0-9]+.*.rpm
python3.11-pbr-[0-9]+.*.rpm
python3.11-pexpect-[0-9]+.*.rpm
python3.11-pillow-[0-9]+.*.rpm
python3.11-pip-wheel-[0-9]+.*.rpm
python3.11-productmd-[0-9]+.*.rpm
python3.11-protobuf-[0-9]+.*.rpm
python3.11-psycopg-[0-9]+.*.rpm
python3.11-ptyprocess-[0-9]+.*.rpm
python3.11-pulp-ansible-[0-9]+.*.rpm
python3.11-pulp-cli-[0-9]+.*.rpm
python3.11-pulp-glue-[0-9]+.*.rpm
python3.11-pulp-rpm-[0-9]+.*.rpm
python3.11-pulpcore-[0-9]+.*.rpm
python3.11-pycares-[0-9]+.*.rpm
python3.11-pycodestyle-[0-9]+.*.rpm
python3.11-pycparser-[0-9]+.*.rpm
python3.11-pyflakes-[0-9]+.*.rpm
python3.11-pygments-[0-9]+.*.rpm
python3.11-pygtrie-[0-9]+.*.rpm
python3.11-pyOpenSSL-[0-9]+.*.rpm
python3.11-pyparsing-[0-9]+.*.rpm
python3.11-pyrsistent-[0-9]+.*.rpm
python3.11-pytz-[0-9]+.*.rpm
python3.11-pyyaml-[0-9]+.*.rpm
python3.11-redis-[0-9]+.*.rpm
python3.11-requests-[0-9]+.*.rpm
python3.11-requirements-parser-[0-9]+.*.rpm
python3.11-rhsm-[0-9]+.*.rpm
python3.11-schema-[0-9]+.*.rpm
python3.11-semantic-version-[0-9]+.*.rpm
python3.11-setuptools-[0-9]+.*.rpm
python3.11-setuptools-wheel-[0-9]+.*.rpm
python3.11-six-[0-9]+.*.rpm
python3.11-smmap-[0-9]+.*.rpm
python3.11-solv-[0-9]+.*.rpm
python3.11-sqlparse-[0-9]+.*.rpm
python3.11-tablib-[0-9]+.*.rpm
python3.11-toml-[0-9]+.*.rpm
python3.11-typing-extensions-[0-9]+.*.rpm
python3.11-uritemplate-[0-9]+.*.rpm
python3.11-url-normalize-[0-9]+.*.rpm
python3.11-urllib3-[0-9]+.*.rpm
python3.11-urlman-[0-9]+.*.rpm
python3.11-uuid6-[0-9]+.*.rpm
python3.11-webencodings-[0-9]+.*.rpm
python3.11-whitenoise-[0-9]+.*.rpm
python3.11-wrapt-[0-9]+.*.rpm
python3.11-xlrd-[0-9]+.*.rpm
python3.11-xlwt-[0-9]+.*.rpm
python3.11-yarl-[0-9]+.*.rpm
python3.11-zipp-[0-9]+.*.rpm
qemu-img-[0-9]+.*.rpm
qemu-kvm-[0-9]+.*.rpm
qemu-kvm-audio-pa-[0-9]+.*.rpm
qemu-kvm-block-blkio-[0-9]+.*.rpm
qemu-kvm-block-curl-[0-9]+.*.rpm
qemu-kvm-block-gluster-[0-9]+.*.rpm
qemu-kvm-block-iscsi-[0-9]+.*.rpm
qemu-kvm-block-rbd-[0-9]+.*.rpm
qemu-kvm-block-ssh-[0-9]+.*.rpm
qemu-kvm-common-[0-9]+.*.rpm
qemu-kvm-core-[0-9]+.*.rpm
qemu-kvm-device-display-virtio-gpu-[0-9]+.*.rpm
qemu-kvm-device-display-virtio-gpu-pci-[0-9]+.*.rpm
qemu-kvm-device-display-virtio-vga-[0-9]+.*.rpm
qemu-kvm-device-usb-host-[0-9]+.*.rpm
qemu-kvm-device-usb-redirect-[0-9]+.*.rpm
qemu-kvm-docs-[0-9]+.*.rpm
qemu-kvm-hw-usbredir-[0-9]+.*.rpm
qemu-kvm-tools-[0-9]+.*.rpm
qemu-kvm-ui-egl-headless-[0-9]+.*.rpm
qemu-kvm-ui-opengl-[0-9]+.*.rpm
qemu-kvm-ui-spice-[0-9]+.*.rpm
qemu-pr-helper-[0-9]+.*.rpm
qt5-srpm-macros-[0-9]+.*.rpm
quota-[0-9]+.*.rpm
quota-nls-[0-9]+.*.rpm
readline-[0-9]+.*.rpm
redhat-cloud-client-configuration-[0-9]+.*.rpm
redhat-logos-httpd-[0-9]+.*.rpm
redhat-release-[0-9]+.*.rpm
redhat-release-eula-[0-9]+.*.rpm
redhat-rpm-config-[0-9]+.*.rpm
redis-[0-9]+.*.rpm
rest-[0-9]+.*.rpm
rh-amazon-rhui-client-[0-9]+.*.rpm
rhc-[0-9]+.*.rpm
rng-tools-[0-9]+.*.rpm
rootfiles-[0-9]+.*.rpm
rpcbind-[0-9]+.*.rpm
rpm-[0-9]+.*.rpm
rpm-build-[0-9]+.*.rpm
rpm-build-libs-[0-9]+.*.rpm
rpm-libs-[0-9]+.*.rpm
rpm-plugin-audit-[0-9]+.*.rpm
rpm-plugin-fapolicyd-[0-9]+.*.rpm
rpm-plugin-selinux-[0-9]+.*.rpm
rpm-plugin-systemd-inhibit-[0-9]+.*.rpm
rpm-sign-libs-[0-9]+.*.rpm
rsync-[0-9]+.*.rpm
rsyslog-[0-9]+.*.rpm
rsyslog-gnutls-[0-9]+.*.rpm
rsyslog-logrotate-[0-9]+.*.rpm
rtkit-[0-9]+.*.rpm
ruby-[0-9]+.*.rpm
ruby-default-gems-[0-9]+.*.rpm
ruby-libs-[0-9]+.*.rpm
rubygem-actioncable-[0-9]+.*.rpm
rubygem-actionmailbox-[0-9]+.*.rpm
rubygem-actionmailer-[0-9]+.*.rpm
rubygem-actionpack-[0-9]+.*.rpm
rubygem-actiontext-[0-9]+.*.rpm
rubygem-actionview-[0-9]+.*.rpm
rubygem-activejob-[0-9]+.*.rpm
rubygem-activemodel-[0-9]+.*.rpm
rubygem-activerecord-[0-9]+.*.rpm
rubygem-activerecord-import-[0-9]+.*.rpm
rubygem-activerecord-session_store-[0-9]+.*.rpm
rubygem-activestorage-[0-9]+.*.rpm
rubygem-activesupport-[0-9]+.*.rpm
rubygem-acts_as_list-[0-9]+.*.rpm
rubygem-addressable-[0-9]+.*.rpm
rubygem-algebrick-[0-9]+.*.rpm
rubygem-amazing_print-[0-9]+.*.rpm
rubygem-ancestry-[0-9]+.*.rpm
rubygem-angular-rails-templates-[0-9]+.*.rpm
rubygem-ansi-[0-9]+.*.rpm
rubygem-apipie-bindings-[0-9]+.*.rpm
rubygem-apipie-dsl-[0-9]+.*.rpm
rubygem-apipie-params-[0-9]+.*.rpm
rubygem-apipie-rails-[0-9]+.*.rpm
rubygem-audited-[0-9]+.*.rpm
rubygem-azure_mgmt_compute-[0-9]+.*.rpm
rubygem-azure_mgmt_network-[0-9]+.*.rpm
rubygem-azure_mgmt_resources-[0-9]+.*.rpm
rubygem-azure_mgmt_storage-[0-9]+.*.rpm
rubygem-azure_mgmt_subscriptions-[0-9]+.*.rpm
rubygem-bcrypt-[0-9]+.*.rpm
rubygem-bigdecimal-[0-9]+.*.rpm
rubygem-builder-[0-9]+.*.rpm
rubygem-bundler_ext-[0-9]+.*.rpm
rubygem-bundler-[0-9]+.*.rpm
rubygem-clamp-[0-9]+.*.rpm
rubygem-colorize-[0-9]+.*.rpm
rubygem-concurrent-ruby-[0-9]+.*.rpm
rubygem-concurrent-ruby-edge-[0-9]+.*.rpm
rubygem-connection_pool-[0-9]+.*.rpm
rubygem-crass-[0-9]+.*.rpm
rubygem-css_parser-[0-9]+.*.rpm
rubygem-daemons-[0-9]+.*.rpm
rubygem-deacon-[0-9]+.*.rpm
rubygem-declarative-[0-9]+.*.rpm
rubygem-deep_cloneable-[0-9]+.*.rpm
rubygem-deface-[0-9]+.*.rpm
rubygem-diffy-[0-9]+.*.rpm
rubygem-domain_name-[0-9]+.*.rpm
rubygem-dynflow-[0-9]+.*.rpm
rubygem-erubi-[0-9]+.*.rpm
rubygem-et-orbi-[0-9]+.*.rpm
rubygem-excon-[0-9]+.*.rpm
rubygem-facter-[0-9]+.*.rpm
rubygem-faraday-[0-9]+.*.rpm
rubygem-faraday-cookie_jar-[0-9]+.*.rpm
rubygem-faraday-em_http-[0-9]+.*.rpm
rubygem-faraday-em_synchrony-[0-9]+.*.rpm
rubygem-faraday-excon-[0-9]+.*.rpm
rubygem-faraday-httpclient-[0-9]+.*.rpm
rubygem-faraday-multipart-[0-9]+.*.rpm
rubygem-faraday-net_http_persistent-[0-9]+.*.rpm
rubygem-faraday-net_http-[0-9]+.*.rpm
rubygem-faraday-patron-[0-9]+.*.rpm
rubygem-faraday-rack-[0-9]+.*.rpm
rubygem-faraday-retry-[0-9]+.*.rpm
rubygem-fast_gettext-[0-9]+.*.rpm
rubygem-ffi-[0-9]+.*.rpm
rubygem-fog-aws-[0-9]+.*.rpm
rubygem-fog-core-[0-9]+.*.rpm
rubygem-fog-json-[0-9]+.*.rpm
rubygem-fog-libvirt-[0-9]+.*.rpm
rubygem-fog-openstack-[0-9]+.*.rpm
rubygem-fog-ovirt-[0-9]+.*.rpm
rubygem-fog-vsphere-[0-9]+.*.rpm
rubygem-fog-xml-[0-9]+.*.rpm
rubygem-foreman_ansible-[0-9]+.*.rpm
rubygem-foreman_azure_rm-[0-9]+.*.rpm
rubygem-foreman_bootdisk-[0-9]+.*.rpm
rubygem-foreman_discovery-[0-9]+.*.rpm
rubygem-foreman_google-[0-9]+.*.rpm
rubygem-foreman_leapp-[0-9]+.*.rpm
rubygem-foreman_maintain-[0-9]+.*.rpm
rubygem-foreman_openscap-[0-9]+.*.rpm
rubygem-foreman_remote_execution-[0-9]+.*.rpm
rubygem-foreman_rh_cloud-[0-9]+.*.rpm
rubygem-foreman_templates-[0-9]+.*.rpm
rubygem-foreman_theme_satellite-[0-9]+.*.rpm
rubygem-foreman_virt_who_configure-[0-9]+.*.rpm
rubygem-foreman_webhooks-[0-9]+.*.rpm
rubygem-foreman-tasks-[0-9]+.*.rpm
rubygem-formatador-[0-9]+.*.rpm
rubygem-friendly_id-[0-9]+.*.rpm
rubygem-fugit-[0-9]+.*.rpm
rubygem-fx-[0-9]+.*.rpm
rubygem-gapic-common-[0-9]+.*.rpm
rubygem-get_process_mem-[0-9]+.*.rpm
rubygem-gettext_i18n_rails-[0-9]+.*.rpm
rubygem-git-[0-9]+.*.rpm
rubygem-gitlab-sidekiq-fetcher-[0-9]+.*.rpm
rubygem-globalid-[0-9]+.*.rpm
rubygem-google-apis-compute_v1-[0-9]+.*.rpm
rubygem-google-apis-core-[0-9]+.*.rpm
rubygem-google-cloud-common-[0-9]+.*.rpm
rubygem-google-cloud-compute-[0-9]+.*.rpm
rubygem-google-cloud-compute-v1-[0-9]+.*.rpm
rubygem-google-cloud-core-[0-9]+.*.rpm
rubygem-google-cloud-env-[0-9]+.*.rpm
rubygem-google-cloud-errors-[0-9]+.*.rpm
rubygem-google-protobuf-[0-9]+.*.rpm
rubygem-googleapis-common-protos-[0-9]+.*.rpm
rubygem-googleapis-common-protos-types-[0-9]+.*.rpm
rubygem-googleauth-[0-9]+.*.rpm
rubygem-graphql-[0-9]+.*.rpm
rubygem-graphql-batch-[0-9]+.*.rpm
rubygem-grpc-[0-9]+.*.rpm
rubygem-gssapi-[0-9]+.*.rpm
rubygem-hammer_cli_foreman_admin-[0-9]+.*.rpm
rubygem-hammer_cli_foreman_ansible-[0-9]+.*.rpm
rubygem-hammer_cli_foreman_azure_rm-[0-9]+.*.rpm
rubygem-hammer_cli_foreman_bootdisk-[0-9]+.*.rpm
rubygem-hammer_cli_foreman_discovery-[0-9]+.*.rpm
rubygem-hammer_cli_foreman_google-[0-9]+.*.rpm
rubygem-hammer_cli_foreman_leapp-[0-9]+.*.rpm
rubygem-hammer_cli_foreman_openscap-[0-9]+.*.rpm
rubygem-hammer_cli_foreman_remote_execution-[0-9]+.*.rpm
rubygem-hammer_cli_foreman_tasks-[0-9]+.*.rpm
rubygem-hammer_cli_foreman_templates-[0-9]+.*.rpm
rubygem-hammer_cli_foreman_virt_who_configure-[0-9]+.*.rpm
rubygem-hammer_cli_foreman_webhooks-[0-9]+.*.rpm
rubygem-hammer_cli_foreman-[0-9]+.*.rpm
rubygem-hammer_cli_katello-[0-9]+.*.rpm
rubygem-hammer_cli-[0-9]+.*.rpm
rubygem-hashie-[0-9]+.*.rpm
rubygem-highline-[0-9]+.*.rpm
rubygem-hocon-[0-9]+.*.rpm
rubygem-http-accept-[0-9]+.*.rpm
rubygem-http-cookie-[0-9]+.*.rpm
rubygem-httpclient-[0-9]+.*.rpm
rubygem-i18n-[0-9]+.*.rpm
rubygem-io-console-[0-9]+.*.rpm
rubygem-irb-[0-9]+.*.rpm
rubygem-json-[0-9]+.*.rpm
rubygem-jwt-[0-9]+.*.rpm
rubygem-kafo_parsers-[0-9]+.*.rpm
rubygem-kafo_wizards-[0-9]+.*.rpm
rubygem-kafo-[0-9]+.*.rpm
rubygem-katello-[0-9]+.*.rpm
rubygem-ldap_fluff-[0-9]+.*.rpm
rubygem-little-plugger-[0-9]+.*.rpm
rubygem-locale-[0-9]+.*.rpm
rubygem-logging-[0-9]+.*.rpm
rubygem-loofah-[0-9]+.*.rpm
rubygem-mail-[0-9]+.*.rpm
rubygem-marcel-[0-9]+.*.rpm
rubygem-memoist-[0-9]+.*.rpm
rubygem-method_source-[0-9]+.*.rpm
rubygem-mime-types-[0-9]+.*.rpm
rubygem-mime-types-data-[0-9]+.*.rpm
rubygem-mini_mime-[0-9]+.*.rpm
rubygem-minitest-[0-9]+.*.rpm
rubygem-mqtt-[0-9]+.*.rpm
rubygem-ms_rest_azure-[0-9]+.*.rpm
rubygem-ms_rest-[0-9]+.*.rpm
rubygem-msgpack-[0-9]+.*.rpm
rubygem-multi_json-[0-9]+.*.rpm
rubygem-multipart-post-[0-9]+.*.rpm
rubygem-mustermann-[0-9]+.*.rpm
rubygem-net_http_unix-[0-9]+.*.rpm
rubygem-net-ldap-[0-9]+.*.rpm
rubygem-net-ping-[0-9]+.*.rpm
rubygem-net-scp-[0-9]+.*.rpm
rubygem-net-ssh-[0-9]+.*.rpm
rubygem-netrc-[0-9]+.*.rpm
rubygem-nio4r-[0-9]+.*.rpm
rubygem-nokogiri-[0-9]+.*.rpm
rubygem-oauth-[0-9]+.*.rpm
rubygem-oauth-tty-[0-9]+.*.rpm
rubygem-openscap_parser-[0-9]+.*.rpm
rubygem-openscap-[0-9]+.*.rpm
rubygem-optimist-[0-9]+.*.rpm
rubygem-os-[0-9]+.*.rpm
rubygem-ovirt-engine-sdk-[0-9]+.*.rpm
rubygem-parallel-[0-9]+.*.rpm
rubygem-pg-[0-9]+.*.rpm
rubygem-polyglot-[0-9]+.*.rpm
rubygem-powerbar-[0-9]+.*.rpm
rubygem-promise.rb-[0-9]+.*.rpm
rubygem-psych-[0-9]+.*.rpm
rubygem-public_suffix-[0-9]+.*.rpm
rubygem-pulp_ansible_client-[0-9]+.*.rpm
rubygem-pulp_certguard_client-[0-9]+.*.rpm
rubygem-pulp_container_client-[0-9]+.*.rpm
rubygem-pulp_deb_client-[0-9]+.*.rpm
rubygem-pulp_file_client-[0-9]+.*.rpm
rubygem-pulp_ostree_client-[0-9]+.*.rpm
rubygem-pulp_python_client-[0-9]+.*.rpm
rubygem-pulp_rpm_client-[0-9]+.*.rpm
rubygem-pulpcore_client-[0-9]+.*.rpm
rubygem-puma-[0-9]+.*.rpm
rubygem-puma-status-[0-9]+.*.rpm
rubygem-raabro-[0-9]+.*.rpm
rubygem-rabl-[0-9]+.*.rpm
rubygem-rack-[0-9]+.*.rpm
rubygem-rack-cors-[0-9]+.*.rpm
rubygem-rack-jsonp-[0-9]+.*.rpm
rubygem-rack-protection-[0-9]+.*.rpm
rubygem-rack-test-[0-9]+.*.rpm
rubygem-rails-[0-9]+.*.rpm
rubygem-rails-dom-testing-[0-9]+.*.rpm
rubygem-rails-html-sanitizer-[0-9]+.*.rpm
rubygem-rails-i18n-[0-9]+.*.rpm
rubygem-railties-[0-9]+.*.rpm
rubygem-rainbow-[0-9]+.*.rpm
rubygem-rake-[0-9]+.*.rpm
rubygem-rb-inotify-[0-9]+.*.rpm
rubygem-rbvmomi2-[0-9]+.*.rpm
rubygem-rchardet-[0-9]+.*.rpm
rubygem-rdoc-[0-9]+.*.rpm
rubygem-redfish_client-[0-9]+.*.rpm
rubygem-redis-[0-9]+.*.rpm
rubygem-representable-[0-9]+.*.rpm
rubygem-responders-[0-9]+.*.rpm
rubygem-rest-client-[0-9]+.*.rpm
rubygem-retriable-[0-9]+.*.rpm
rubygem-rexml-[0-9]+.*.rpm
rubygem-rkerberos-[0-9]+.*.rpm
rubygem-roadie-[0-9]+.*.rpm
rubygem-roadie-rails-[0-9]+.*.rpm
rubygem-rsec-[0-9]+.*.rpm
rubygem-rss-[0-9]+.*.rpm
rubygem-ruby_parser-[0-9]+.*.rpm
rubygem-ruby-libvirt-[0-9]+.*.rpm
rubygem-ruby2_keywords-[0-9]+.*.rpm
rubygem-ruby2ruby-[0-9]+.*.rpm
rubygem-rubyipmi-[0-9]+.*.rpm
rubygem-safemode-[0-9]+.*.rpm
rubygem-scoped_search-[0-9]+.*.rpm
rubygem-sd_notify-[0-9]+.*.rpm
rubygem-secure_headers-[0-9]+.*.rpm
rubygem-sequel-[0-9]+.*.rpm
rubygem-server_sent_events-[0-9]+.*.rpm
rubygem-sexp_processor-[0-9]+.*.rpm
rubygem-sidekiq-[0-9]+.*.rpm
rubygem-signet-[0-9]+.*.rpm
rubygem-sinatra-[0-9]+.*.rpm
rubygem-smart_proxy_ansible-[0-9]+.*.rpm
rubygem-smart_proxy_dynflow-[0-9]+.*.rpm
rubygem-smart_proxy_openscap-[0-9]+.*.rpm
rubygem-smart_proxy_pulp-[0-9]+.*.rpm
rubygem-smart_proxy_remote_execution_ssh-[0-9]+.*.rpm
rubygem-snaky_hash-[0-9]+.*.rpm
rubygem-spidr-[0-9]+.*.rpm
rubygem-sprockets-[0-9]+.*.rpm
rubygem-sprockets-rails-[0-9]+.*.rpm
rubygem-sqlite3-[0-9]+.*.rpm
rubygem-sshkey-[0-9]+.*.rpm
rubygem-statsd-instrument-[0-9]+.*.rpm
rubygem-stomp-[0-9]+.*.rpm
rubygem-thor-[0-9]+.*.rpm
rubygem-tilt-[0-9]+.*.rpm
rubygem-timeliness-[0-9]+.*.rpm
rubygem-trailblazer-option-[0-9]+.*.rpm
rubygem-tzinfo-[0-9]+.*.rpm
rubygem-uber-[0-9]+.*.rpm
rubygem-unicode-display_width-[0-9]+.*.rpm
rubygem-validates_lengths_from_database-[0-9]+.*.rpm
rubygem-version_gem-[0-9]+.*.rpm
rubygem-webrick-[0-9]+.*.rpm
rubygem-websocket-driver-[0-9]+.*.rpm
rubygem-websocket-extensions-[0-9]+.*.rpm
rubygem-will_paginate-[0-9]+.*.rpm
rubygem-xmlrpc-[0-9]+.*.rpm
rubygem-zeitwerk-[0-9]+.*.rpm
rubygems-[0-9]+.*.rpm
rust-srpm-macros-[0-9]+.*.rpm
s-nail-[0-9]+.*.rpm
satellite-[0-9]+.*.rpm
satellite-cli-[0-9]+.*.rpm
satellite-common-[0-9]+.*.rpm
satellite-installer-[0-9]+.*.rpm
satellite-lifecycle-[0-9]+.*.rpm
satellite-maintain-[0-9]+.*.rpm
scap-security-guide-[0-9]+.*.rpm
scrub-[0-9]+.*.rpm
seabios-bin-[0-9]+.*.rpm
seavgabios-bin-[0-9]+.*.rpm
sed-[0-9]+.*.rpm
selinux-policy-[0-9]+.*.rpm
selinux-policy-targeted-[0-9]+.*.rpm
setup-[0-9]+.*.rpm
sg3_utils-[0-9]+.*.rpm
sg3_utils-libs-[0-9]+.*.rpm
sgabios-bin-[0-9]+.*.rpm
shadow-utils-[0-9]+.*.rpm
shadow-utils-subid-[0-9]+.*.rpm
shared-mime-info-[0-9]+.*.rpm
shim-x64-[0-9]+.*.rpm
slang-[0-9]+.*.rpm
slirp4netns-[0-9]+.*.rpm
snappy-[0-9]+.*.rpm
sound-theme-freedesktop-[0-9]+.*.rpm
spice-server-[0-9]+.*.rpm
sqlite-libs-[0-9]+.*.rpm
squashfs-tools-[0-9]+.*.rpm
sshpass-[0-9]+.*.rpm
sssd-client-[0-9]+.*.rpm
sssd-common-[0-9]+.*.rpm
sssd-kcm-[0-9]+.*.rpm
sssd-nfs-idmap-[0-9]+.*.rpm
subscription-manager-[0-9]+.*.rpm
subscription-manager-rhsm-certificates-[0-9]+.*.rpm
sudo-[0-9]+.*.rpm
swtpm-[0-9]+.*.rpm
swtpm-libs-[0-9]+.*.rpm
swtpm-tools-[0-9]+.*.rpm
syslinux-[0-9]+.*.rpm
syslinux-nonlinux-[0-9]+.*.rpm
systemd-[0-9]+.*.rpm
systemd-container-[0-9]+.*.rpm
systemd-libs-[0-9]+.*.rpm
systemd-pam-[0-9]+.*.rpm
systemd-rpm-macros-[0-9]+.*.rpm
systemd-udev-[0-9]+.*.rpm
tar-[0-9]+.*.rpm
tcpdump-[0-9]+.*.rpm
teamd-[0-9]+.*.rpm
tftp-server-[0-9]+.*.rpm
tmux-[0-9]+.*.rpm
tomcat-[0-9]+.*.rpm
tomcat-el-[0-9]+.*.rpm
tomcat-jsp-[0-9]+.*.rpm
tomcat-lib-[0-9]+.*.rpm
tomcat-servlet-[0-9]+.*.rpm
totem-pl-parser-[0-9]+.*.rpm
tpm2-tools-[0-9]+.*.rpm
tpm2-tss-[0-9]+.*.rpm
traceroute-[0-9]+.*.rpm
tracker-[0-9]+.*.rpm
tracker-miners-[0-9]+.*.rpm
ttmkfdir-[0-9]+.*.rpm
tuned-[0-9]+.*.rpm
tzdata-[0-9]+.*.rpm
tzdata-2024a-[0-9]+.*.rpm
tzdata-java-[0-9]+.*.rpm
udisks2-[0-9]+.*.rpm
unbound-libs-[0-9]+.*.rpm
unzip-[0-9]+.*.rpm
upower-[0-9]+.*.rpm
usbguard-[0-9]+.*.rpm
usbguard-selinux-[0-9]+.*.rpm
usbredir-[0-9]+.*.rpm
usermode-[0-9]+.*.rpm
userspace-rcu-[0-9]+.*.rpm
util-linux-[0-9]+.*.rpm
util-linux-core-[0-9]+.*.rpm
uuid-[0-9]+.*.rpm
vim-common-[0-9]+.*.rpm
vim-enhanced-[0-9]+.*.rpm
vim-filesystem-[0-9]+.*.rpm
vim-minimal-[0-9]+.*.rpm
virt-what-[0-9]+.*.rpm
virtiofsd-[0-9]+.*.rpm
volume_key-libs-[0-9]+.*.rpm
webrtc-audio-processing-[0-9]+.*.rpm
wget-[0-9]+.*.rpm
which-[0-9]+.*.rpm
wireplumber-[0-9]+.*.rpm
wireplumber-libs-[0-9]+.*.rpm
xdg-dbus-proxy-[0-9]+.*.rpm
xdg-desktop-portal-[0-9]+.*.rpm
xdg-desktop-portal-gtk-[0-9]+.*.rpm
xfsprogs-[0-9]+.*.rpm
xkeyboard-config-[0-9]+.*.rpm
xml-common-[0-9]+.*.rpm
xmlsec1-[0-9]+.*.rpm
xmlsec1-openssl-[0-9]+.*.rpm
xorg-x11-fonts-Type1-[0-9]+.*.rpm
xorriso-[0-9]+.*.rpm
xz-[0-9]+.*.rpm
xz-libs-[0-9]+.*.rpm
yajl-[0-9]+.*.rpm
yum-[0-9]+.*.rpm
yum-utils-[0-9]+.*.rpm
zip-[0-9]+.*.rpm
zlib-[0-9]+.*.rpm
zstd-[0-9]+.*.rpm