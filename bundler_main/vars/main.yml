# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2025-08-05 espy & <PERSON><PERSON>                                    #
#               2025-21-07 espy                                                   #
# Create Date:  2025-21-07                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

direct_openshift_packages_mirror: "https://mirror.openshift.com/pub/openshift-v4/x86_64"
openshift_packages_mirror: "https://{{ bundler.nexus_domain }}/repository/openshift/x86_64"
nexus_image: docker.io/sonatype/nexus3:{{ bundler.nexus_image_version }}
galaxy_image: harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy:{{ galaxy.version }}
original_rhel_iso_location: "/tmp/{{ bundler.rhel_iso_name }}"
packages_file: "/tmp/packages.txt"
modified_iso_location: "/tmp/modified_iso"
coreos_qemu_name: "{{ coreos_qemu_uri.value.split('/') | last }}"
ocp_channel: "stable-{{ ocp_base_version }}"
ocp_base_version: "{{ bundler.openshift_version | split('.') | slice(2) | first | join('.') }}"
architecture: 'amd64'
container_name: nexus
default_nexus_url: "https://{{ bundler.default_nexus_url }}"
galaxy_nexus_api: "{{ default_nexus_url }}/service/rest/v1"
default_registry_url: "{{ bundler.default_registry_url }}"
large_directory: "/opt"
apps_install_path: "{{ large_directory }}/openshift"
nexus_data_dir: "{{ apps_install_path }}/nexus-data"
openshift_mirror_proxy: "{{ bundler.nexus_domain }}/repository/openshift"
install_tar_name: "{{ 'openshift-install-rhel9-amd64.tar.gz' if bundler.openshift_version.split('.')[1] | int >= 16 else 'openshift-install-linux.tar.gz' }}"
openshift_installer_url: "https://{{ openshift_mirror_proxy }}/clients/ocp/{{ bundler.openshift_version }}/{{ install_tar_name }}"
github_proxy: "{{ bundler.nexus_domain }}/repository/github"
github_okd_base_url: "https://{{ github_proxy }}/okd-project/okd/releases/download"
okd_installer_url: "{{ github_okd_base_url }}/{{ bundler.okd_version }}/openshift-install-linux-{{ bundler.okd_version }}.tar.gz"
installer_url: >-
  {{
    openshift_installer_url if
    bundler.distro in ['ocp', 'sno'] else
    okd_installer_url if
    bundler.distro == 'okd' else
    None }}
role_required_vars:
  - bundler.nexus_storage
  - bundler.verson_vars_file
  - bundler.openshift_install_file
  - bundler.openshift_version
  - bundler.redhat_registry_pull_secret_file or proxying_mirror
  - bundler.rhel_iso_name
galaxy_nexus_repo_files_dir: "{{ large_directory }}/nexus_files"
package_files: "{{ large_directory }}/package_files"
package_files_galaxy_folder: "{{ package_files }}{{ galaxy_folder }}"
no_proxy: "{{ (bundler.standard_no_proxy_addresses + ((domain_mappings | map(attribute='destination')) if proxying_mirror else [])) | join(',') }}"
nexus_base_url: "{{ bundler.nexus_domain | regex_replace('^[^.]+\\.', '') }}"
domain_mappings:
  - destination: docker.io
    reroute: docker-io-nexus.{{ nexus_base_url }}
  - destination: registry-1.docker.io
    reroute: docker-io-nexus.{{ nexus_base_url }}
  - destination: gcr.io
    reroute: gcr-io-nexus.{{ nexus_base_url }}
  - destination: ghcr.io
    reroute: ghcr-io-nexus.{{ nexus_base_url }}
  - destination: k8s.gcr.io
    reroute: k8s-gcr-io-nexus.{{ nexus_base_url }}
  - destination: icr.io
    reroute: icr-io-nexus.{{ nexus_base_url }}
  - destination: cp.icr.io
    reroute: cp-icr-io-nexus.{{ nexus_base_url }}
  - destination: nvcr.io
    reroute: nvcr-io-nexus.{{ nexus_base_url }}
  - destination: quay.io
    reroute: quay-io-nexus.{{ nexus_base_url }}
  - destination: registry.access.redhat.com
    reroute: redhat-access-nexus.{{ nexus_base_url }}
  - destination: registry.connect.redhat.com
    reroute: redhat-connect-nexus.{{ nexus_base_url }}
  - destination: registry.gitlab.com
    reroute: gitlab-nexus.{{ nexus_base_url }}
  - destination: registry.redhat.io
    reroute: redhat-io-nexus.{{ nexus_base_url }}
  - destination: registry.k8s.io
    reroute: k8s-io-nexus.{{ nexus_base_url }}
  - destination: registry.ci.openshift.org
    reroute: registry-ci-openshift-org-nexus.{{ nexus_base_url }}
  - destination: harbor.us.lmco.com
    reroute: harbor-us-nexus.{{ nexus_base_url }}
  - destination: harbor.global.lmco.com
    reroute: harbor-global-nexus.{{ nexus_base_url }}
aap_ee_image_name: "harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy-aap"
install_file_name: "{{ 'openshift-install-fips' if bundler.openshift_version.split('.')[1] | int >= 16 else 'openshift-install' }}"
