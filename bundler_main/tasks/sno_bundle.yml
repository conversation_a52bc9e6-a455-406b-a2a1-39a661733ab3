# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2025-31-07 espy                                                   #
# Create Date:  2025-31-07                                                        #
# Author:       <PERSON><PERSON> & Espy                                               #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: sno_bundle | Setup container galaxy folder link to package files
  ansible.builtin.include_tasks: setup_container_galaxy_folder_link.yml

- name: sno_bundle | Get baremetal installer
  ansible.builtin.include_tasks: get_baremetal_installer.yml

- name: sno_bundle | Get sno file
  ansible.builtin.include_tasks: get_sno_file.yml

- name: sno_bundle | Download Bundle Files
  ansible.builtin.include_tasks: download_bundle_files.yml

- name: sno_bundle | Upload galaxy bundle
  ansible.builtin.include_tasks: upload_galaxy_bundle.yml
