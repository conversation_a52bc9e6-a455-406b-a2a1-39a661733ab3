---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        getting_add_img_list.yml                                          #
# Version:                                                                        #
#               2025-08-05 Initial                                                #
# Create Date:  2025-08-05                                                        #
# Author:       <PERSON><PERSON>, <PERSON> (<EMAIL>)                             #
# Description:                                                                    #
#               Initial commit for getting_add_img_list.yml                       #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: get software name
  ansible.builtin.set_fact:
    software_name: "{{ json_data_results_additional_images.results[idx].item.item | split('/') }}"

- name: get software version number
  ansible.builtin.set_fact:
    software_version_number: "{{ role.software_version }}"
  loop: "{{ combined_role_list }}"
  loop_control:
    loop_var: role
  when: role.name.split('.')[1] == software_name[7]

- name: get add_img_list
  ansible.builtin.set_fact:
    add_img_list: >-
      {{
        add_img_list | default([]) +
        [json_data_results_additional_images.results[idx].ansible_facts.json_data.additionalImages[software_version_number]]
      }}
