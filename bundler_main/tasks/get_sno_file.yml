# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2025-31-07 espy                                                   #
# Create Date:  2025-31-07                                                        #
# Author:       <PERSON><PERSON> & Espy                                               #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: get_sno_file | Get ISO URL
  ansible.builtin.shell:
    cmd: set -o pipefail && openshift-install coreos print-stream-json | jq -r .architectures.x86_64.artifacts.metal.formats.iso.disk.location
  register: iso_output
  changed_when: false

- name: get_sno_file | Download RHCOS ISO
  ansible.builtin.get_url:
    url: "{{ iso_output.stdout }}"
    dest: "{{ package_files }}/rhcos-live.iso"
    mode: '0644'
