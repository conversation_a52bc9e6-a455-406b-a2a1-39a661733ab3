---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        getting_img_set_list.yml                                          #
# Version:                                                                        #
#               2025-08-05 Initial                                                #
# Create Date:  2025-08-05                                                        #
# Author:       <PERSON><PERSON>, <PERSON> (<EMAIL>)                             #
# Description:                                                                    #
#               Initial commit for getting_img_set_list.yml                       #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: get software name
  ansible.builtin.set_fact:
    software_name: "{{ json_data_results_image_sets.results[idx].item.item | split('/') }}"

- name: get software version number
  ansible.builtin.set_fact:
    software_version_number: "{{ role.software_version }}"
  loop: "{{ combined_role_list }}"
  loop_control:
    loop_var: role
  when: role.name.split('.')[1] == software_name[7]

- name: get img_set_list
  ansible.builtin.set_fact:
    img_set_list: >-
      {{
        img_set_list | default([]) +
        [json_data_results_image_sets.results[idx].ansible_facts.json_data.imageSetConfiguration[software_version_number]]
      }}
