# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        package_nexus_data.yml                                            #
# Version:                                                                        #
#               2025-21-07 espy                                                   #
# Create Date:  2025-21-07                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: package_nexus_data | Create nexus task to compact blobstore
  ansible.builtin.uri:
    url: "{{ default_nexus_url }}/service/extdirect"
    method: "POST"
    user: "{{ bundler.galaxy_nexus_user }}"
    password: "{{ bundler.galaxy_nexus_password }}"
    force_basic_auth: true
    body:
      action: coreui_Task
      method: create
      data:
        - name: delete-directpath
          id: NX.coreui.model.Task-2
          typeId: blobstore.compact
          enabled: true
          alertEmail: ""
          notificationCondition: FAILURE
          schedule: manual
          properties:
            blobstoreName: default
          recurringDays: []
          startDate: null
          timeZoneOffset: "-04:00"
      type: rpc
      tid: 999
    body_format: json
    status_code:
      - 200
    return_content: true
  register: compact_blob_task

- name: package_nexus_data | Run compact blob task
  ansible.builtin.uri:
    url: "{{ galaxy_nexus_api }}/tasks/{{ task_id }}/run"
    method: "POST"
    user: "{{ bundler.galaxy_nexus_user }}"
    password: "{{ bundler.galaxy_nexus_password }}"
    force_basic_auth: true
    status_code:
      - 204
    follow_redirects: all
  when: compact_blob_task
  vars:
    task_id: "{{ compact_blob_task.json.result.data.id }}"

- name: package_nexus_data | Wait for compact task to finish
  ansible.builtin.uri:
    url: "{{ galaxy_nexus_api }}/tasks/{{ task_id }}"
    method: "GET"
    user: "{{ bundler.galaxy_nexus_user }}"
    password: "{{ bundler.galaxy_nexus_password }}"
    force_basic_auth: true
    follow_redirects: all
  register: job_status
  retries: 60
  until: job_status.json.lastRunResult == "OK"
  delay: 10
  vars:
    task_id: "{{ compact_blob_task.json.result.data.id }}"

- name: package_nexus_data | Stop nexus container
  containers.podman.podman_container:
    name: "{{ container_name }}"
    state: stopped

- name: package_nexus_data | Copy over nexus logs to nexus directory
  ansible.builtin.shell:
    cmd: set -o pipefail && podman logs {{ container_name }} > nexus-build.log
    chdir: "{{ nexus_data_dir }}"
    creates: "{{ nexus_data_dir }}/nexus-build.log"

- name: package_nexus_data | Tarball directory {{ nexus_data_dir }}
  community.general.archive:
    path: "{{ nexus_data_dir }}"
    dest: "{{ package_files_galaxy_folder }}/{{ bundler.nexus_storage }}"
    format: "tar"
    mode: "0644"
    owner: ansible
    group: ansible
  become: true
  become_user: root

- name: package_nexus_data | Remove nexus container
  containers.podman.podman_container:
    name: "{{ container_name }}"
    state: absent

- name: package_nexus_data | Relieve even more space
  ansible.builtin.file:
    state: absent
    path: "{{ nexus_data_dir }}"
  when: not bundler.developer_run
