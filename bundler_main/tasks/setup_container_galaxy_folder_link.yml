# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        setup_container_galaxy_folder_link.yml                            #
# Version:                                                                        #
#               2025-21-07 espy                                                   #
# Create Date:  2025-21-07                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: setup_container_galaxy_folder_link | Open permissions on opt
  ansible.builtin.file:
    path: "{{ large_directory }}"
    mode: "0777"
    state: directory
  become: true
  become_user: root

- name: setup_container_galaxy_folder_link | Create directories
  ansible.builtin.file:
    path: "{{ item }}"
    mode: "0755"
    state: directory
  loop:
    - "{{ package_files_galaxy_folder }}"

- name: setup_container_galaxy_folder_link | Link galaxy folder to package folder
  ansible.builtin.file:
    src: "{{ package_files_galaxy_folder }}"
    dest: "{{ galaxy_folder }}"
    state: link
    owner: ansible
    group: ansible
    force: true
  become: true
  become_user: root
