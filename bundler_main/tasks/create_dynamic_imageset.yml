# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        create_dynamic_imageset.yml                                       #
# Version:                                                                        #
#               2025-21-07 espy                                                   #
# Create Date:  2025-21-07                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: loop through products and create role list
  ansible.builtin.include_tasks: role_list.yml
  loop: "{{ lmco_openshift_products }}"
  loop_control:
    loop_var: product
  when: product.target in ["redhat_openshift", "okd"]

- name: Create a list of role names
  ansible.builtin.set_fact:
    role_names_list: "{{ role_names_list | default([]) + [item.name] | unique }}"
  loop: "{{ combined_role_list }}"
  when: combined_role_list is defined

- name: Assets file location - imageSetConfigurations
  ansible.builtin.set_fact:
    role_paths_is: >-
      {{
        role_paths_is | default([]) + ['/home/<USER>/.ansible/collections/ansible_collections/'
        + item.split('.')[0] + '/' + item.split('.')[1] + '/roles/' + item.split('.')[-1] |
        replace('_operator', '_assets') | replace('_k8s', '_assets') + '/files/imageSetConfiguration.yml']
      }}
  loop: "{{ role_names_list | unique }}"
  when: role_names_list is defined

- name: Assets file location - additionalImages
  ansible.builtin.set_fact:
    role_paths_ad: >-
      {{
        role_paths_ad | default([]) +
        ['/home/<USER>/.ansible/collections/ansible_collections/'
        + item.split('.')[0] + '/' + item.split('.')[1] + '/roles/'
        + item.split('.')[-1] |
        replace('_operator', '_assets') |
        replace('_k8s', '_assets') + '/files/additionalImages.yml']
      }}
  loop: "{{ role_names_list }}"
  when: role_names_list is defined

- name: Check if the template files exists - imagesetConfigurations
  ansible.builtin.stat:
    path: "{{ item }}"
  register: template_files_stats_is
  loop: "{{ role_paths_is }}"
  when: role_paths_is is defined

- name: Convert Jinja2 templates to JSON - imagesetConfigurations
  ansible.builtin.set_fact:
    json_data: "{{ lookup('template', item.item) | from_yaml | default({}) }}"
  loop: "{{ template_files_stats_is.results | default([]) }}"
  register: json_data_results_image_sets
  when: item.stat.exists

- name: Combine JSON data - img_set_list
  ansible.builtin.include_tasks: getting_img_set_list.yml
  loop: "{{ range(0, json_data_results_image_sets.results | length) }}"
  loop_control:
    index_var: idx
  when: json_data_results_image_sets.results[idx].ansible_facts.json_data is defined

- name: Check if the template files exists - additionalImages
  ansible.builtin.stat:
    path: "{{ item }}"
  register: template_files_stats_ad
  loop: "{{ role_paths_ad | unique }}"
  when: role_paths_ad is defined

- name: Convert Jinja2 templates to JSON - additionalImages
  ansible.builtin.set_fact:
    json_data: "{{ lookup('template', item.item) | from_yaml | default({}) }}"
  loop: "{{ template_files_stats_ad.results | default([]) }}"
  register: json_data_results_additional_images
  when: item.stat.exists

- name: Combine JSON data - add_img_list
  ansible.builtin.include_tasks: getting_add_img_list.yml
  loop: "{{ range(0, json_data_results_additional_images.results | length) }}"
  loop_control:
    index_var: idx
  when: json_data_results_additional_images.results[idx].ansible_facts.json_data is defined

- name: Add default additonal images
  ansible.builtin.set_fact:
    add_img_list: >-
      {{
        add_img_list | default([]) + (lookup('file', 'default-additonal-images.yaml') | from_yaml)
      }}

- name: Combine JSON data - add_img_list
  ansible.builtin.set_fact:
    add_img_list: "{{ add_img_list | default([]) | flatten | unique }}"

- name: Write additonal image list to file
  ansible.builtin.copy:
    content: "{{ add_img_list | to_nice_json }}"
    dest: "{{ role_path }}/files/additonal_image_list.yaml"
    mode: "0644"

- name: Combine JSON data - img_set_list
  ansible.builtin.set_fact:
    img_set_list: "{{ img_set_list | default([]) | flatten | unique }}"

### ROI ###

- name: Convert Jinja2 templates to JSON - imagesetConfigurations - roi
  ansible.builtin.set_fact:
    roi: "{{ img_set_list | selectattr('roi', 'defined') | map(attribute='roi') | flatten | combine(recursive=true) }}"

- name: Setup ROI operator index values
  when: roi
  block:
    - name: Convert Jinja2 templates to JSON - imagesetConfigurations - roi
      ansible.builtin.set_fact:
        roi_list: "{{ roi_list | default([]) + (item.roi | default([])) }}"
      with_items: "{{ img_set_list }}"

    - name: Combine list of dictionaries combined_roi_list
      ansible.builtin.set_fact:
        combined_roi_list: "{{ roi_list | selectattr('name', 'defined') | list }}"

    - name: Combine list of dictionaries combined_roi_list_without_name
      ansible.builtin.set_fact:
        combined_roi_list_without_name: "{{ roi_list | rejectattr('name', 'defined') | list }}"

    - name: Join variables based on specific keys combined_dict_roi
      ansible.builtin.set_fact:
        combined_dict_roi: {}

    - name: Join variables based on specific keys combined_dict_roi
      ansible.builtin.set_fact:
        combined_dict_roi: >-
          {{
            combined_dict_roi | combine({ item.keys() | first: (combined_dict_roi[item.keys() | first ] | default([])) + item[item.keys() | first]   })
          }}
      loop: "{{ combined_roi_list_without_name }}"
      loop_control:
        label: "{{ item.keys() | first }}"

    - name: Set roi to combined_dict_roi
      ansible.builtin.set_fact:
        roi: "{{ combined_dict_roi }}"

### COI ###
- name: Convert Jinja2 templates to JSON - imagesetConfigurations - coi
  ansible.builtin.set_fact:
    coi: "{{ img_set_list | selectattr('coi', 'defined') | map(attribute='coi') | flatten | combine(recursive=true) }}"

- name: Setup coi operator index values
  when: coi
  block:

    - name: Convert Jinja2 templates to JSON - imagesetConfigurations - coi
      ansible.builtin.set_fact:
        coi_list: "{{ coi_list | default([]) + (item.coi | default([])) }}"
      with_items: "{{ img_set_list }}"

    - name: Combine list of dictionaries combined_coi_list
      ansible.builtin.set_fact:
        combined_coi_list: "{{ coi_list | selectattr('name', 'defined') | list }}"

    - name: Combine list of dictionaries combined_coi_list_without_name
      ansible.builtin.set_fact:
        combined_coi_list_without_name: "{{ coi_list | rejectattr('name', 'defined') | list }}"

    - name: Join variables based on specific keys combined_dict_coi
      ansible.builtin.set_fact:
        combined_dict_coi: {}

    - name: Join variables based on specific keys combined_dict_coi
      ansible.builtin.set_fact:
        combined_dict_coi: >-
          {{
            combined_dict_coi | combine({ item.keys() | first: (combined_dict_coi[item.keys() | first ] | default([])) + item[item.keys() | first]   })
          }}
      loop: "{{ combined_coi_list_without_name }}"
      loop_control:
        label: "{{ item.keys() | first }}"

    - name: Set coi to combined_dict_coi
      ansible.builtin.set_fact:
        coi: "{{ combined_dict_coi }}"

### COMOI ###
- name: Convert Jinja2 templates to JSON - imagesetConfigurations - comoi
  ansible.builtin.set_fact:
    comoi: "{{ img_set_list | selectattr('comoi', 'defined') | map(attribute='comoi') | flatten | combine(recursive=true) }}"

- name: Setup comoi operator index values
  when: comoi
  block:

    - name: Convert Jinja2 templates to JSON - imagesetConfigurations - comoi
      ansible.builtin.set_fact:
        comoi_list: "{{ comoi_list | default([]) + (item.comoi | default([])) }}"
      with_items: "{{ img_set_list }}"

    - name: Combine list of dictionaries combined_comoi_list
      ansible.builtin.set_fact:
        combined_comoi_list: "{{ comoi_list | selectattr('name', 'defined') | list }}"

    - name: Combine list of dictionaries combined_comoi_list_without_name
      ansible.builtin.set_fact:
        combined_comoi_list_without_name: "{{ comoi_list | rejectattr('name', 'defined') | list }}"

    - name: Join variables based on specific keys combined_dict_comoi
      ansible.builtin.set_fact:
        combined_dict_comoi: {}

    - name: Join variables based on specific keys combined_dict_comoi
      ansible.builtin.set_fact:
        combined_dict_comoi: >-
          {{
            combined_dict_comoi | combine({ item.keys() | first: (combined_dict_comoi[item.keys() | first ] | default([])) + item[item.keys() | first]   })
          }}
      loop: "{{ combined_comoi_list_without_name }}"
      loop_control:
        label: "{{ item.keys() | first }}"

    - name: Set comoi to combined_dict_comoi
      ansible.builtin.set_fact:
        comoi: "{{ combined_dict_comoi }}"

### CS ###
- name: Convert Jinja2 templates to JSON - imagesetConfigurations - cs
  ansible.builtin.set_fact:
    cs: "{{ img_set_list | selectattr('cs', 'defined') | map(attribute='cs') | flatten | combine(recursive=true) }}"

- name: Setup cs operator index values
  when: cs
  block:

    - name: Convert Jinja2 templates to JSON - imagesetConfigurations - cs
      ansible.builtin.set_fact:
        cs_list: "{{ cs_list | default([]) + (item.cs | default([])) }}"
      with_items: "{{ img_set_list }}"

    - name: Combine list of dictionaries combined_cs_list
      ansible.builtin.set_fact:
        combined_cs_list: "{{ cs_list | selectattr('name', 'defined') | list }}"

    - name: Combine list of dictionaries combined_cs_list_without_name
      ansible.builtin.set_fact:
        combined_cs_list_without_name: "{{ cs_list | rejectattr('name', 'defined') | list }}"

    - name: Join variables based on specific keys combined_dict_cs
      ansible.builtin.set_fact:
        combined_dict_cs: {}

    - name: Join variables based on specific keys combined_dict_cs
      ansible.builtin.set_fact:
        combined_dict_cs: >-
          {{
            combined_dict_cs | combine({ item.keys() | first: (combined_dict_cs[item.keys() | first ] | default([])) + item[item.keys() | first]   })
          }}
      loop: "{{ combined_cs_list_without_name }}"
      loop_control:
        label: "{{ item.keys() | first }}"

    - name: Set cs to combined_dict_cs
      ansible.builtin.set_fact:
        cs: "{{ combined_dict_cs }}"

### IBM ###
- name: Convert Jinja2 templates to JSON - imagesetConfigurations - ibm
  ansible.builtin.set_fact:
    ibm: "{{ img_set_list | selectattr('ibm', 'defined') | map(attribute='ibm') | flatten | combine(recursive=true) }}"

- name: Setup ibm operator index values
  when: ibm
  block:

    - name: Convert Jinja2 templates to JSON - imagesetConfigurations - ibm
      ansible.builtin.set_fact:
        ibm_list: "{{ ibm_list | default([]) + (item.ibm | default([])) }}"
      with_items: "{{ img_set_list }}"

    - name: Combine list of dictionaries combined_ibm_list
      ansible.builtin.set_fact:
        combined_ibm_list: "{{ ibm_list | selectattr('name', 'defined') | list }}"

    - name: Combine list of dictionaries combined_ibm_list_without_name
      ansible.builtin.set_fact:
        combined_ibm_list_without_name: "{{ ibm_list | rejectattr('name', 'defined') | list }}"

    - name: Join variables based on specific keys combined_dict_ibm
      ansible.builtin.set_fact:
        combined_dict_ibm: {}

    - name: Join variables based on specific keys combined_dict_ibm
      ansible.builtin.set_fact:
        combined_dict_ibm: >-
          {{
            combined_dict_ibm | combine({ item.keys() | first: (combined_dict_ibm[item.keys() | first ] | default([])) + item[item.keys() | first]   })
          }}
      loop: "{{ combined_ibm_list_without_name }}"
      loop_control:
        label: "{{ item.keys() | first }}"

    - name: Set ibm to combined_dict_ibm
      ansible.builtin.set_fact:
        ibm: "{{ combined_dict_ibm }}"

- name: Create ImageSetConfiguration object
  ansible.builtin.template:
    src: "{{ role_path }}/templates/dynamic-image-set-configuration.yaml.j2"
    dest: "{{ role_path }}/templates/image-set-configuration.yaml.j2"
    mode: '0775'

- name: SET - img_set_list_icsp_map
  ansible.builtin.set_fact:
    img_set_list_icsp_map: "{{ add_img_list | map(attribute='name') | list }}"

- name: Extract first part of each item
  ansible.builtin.set_fact:
    img_set_list_icsp_map_modified: "{{ img_set_list_icsp_map | map('regex_replace', '^(.+)/[^/]+$', '\\1') | unique }}"

- name: Create generic ICSP value to overwrite the ICSP values
  ansible.builtin.template:
    src: "{{ role_path }}/templates/generic-0.j2"
    dest: "{{ role_path }}/templates/generic-0_additional_images.j2"
    mode: '0775'
