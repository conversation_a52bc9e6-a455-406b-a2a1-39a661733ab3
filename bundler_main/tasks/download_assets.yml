# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        download_assets.yml                                               #
# Version:                                                                        #
#               2025-21-07 espy                                                   #
# Create Date:  2025-21-07                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: download_assets | Include variables from roles.yml
  ansible.builtin.include_vars:
    file: "{{ basepath }}/{{ item.name }}/vars/{{ item.target }}/{{ item.provider }}/{{ item.profile }}/roles.yml"
    name: "dynamic_imageset_galaxy_roles_{{ idx }}"
  loop: "{{ lmco_openshift_products }}"
  loop_control:
    index_var: idx
  vars:
    basepath: /home/<USER>/.ansible/collections/ansible_collections/lmco/galaxy/roles

- name: download_assets | Append variables to an existing variable
  ansible.builtin.set_fact:
    combined_imageset_galaxy_roles: >-
      {{
        combined_imageset_galaxy_roles | default([]) + ([lookup('vars', 'dynamic_imageset_galaxy_roles_' + idx | string) ])
      }}
  loop: "{{ lmco_openshift_products }}"
  loop_control:
    index_var: idx
  when: item.target == "redhat_openshift"

- name: download_assets | Create a list of role names
  ansible.builtin.set_fact:
    role_names_list: "{{ role_names_list | default([]) + [item.name] }}"
  loop: "{{ combined_imageset_galaxy_roles | flatten | map(attribute='galaxy_bundle') | flatten | map(attribute='galaxy_roles') | flatten }}"
  when: combined_imageset_galaxy_roles is defined

- name: download_assets | Assets main.yml file location - _assets/tasks/main.yml
  ansible.builtin.set_fact:
    assets_task_file: >-
      {{
        assets_task_file | default([]) +
        [
          item_path |
          replace('_operator', '_assets') |
          replace('_k8s', '_assets') + '/tasks/main.yml'
        ]
      }}
  loop: "{{ role_names_list }}"
  when: role_names_list is defined
  vars:
    basepath: /home/<USER>/.ansible/collections/ansible_collections
    item_path: "{{ basepath }}/{{ item.split('.')[0] }}/{{ item.split('.')[1] }}/roles/{{ item.split('.')[-1] }}"

- name: download_assets | Check if the template files exists - _assets/task/main.yml
  ansible.builtin.stat:
    path: "{{ item }}"
  register: template_files_stats_is
  loop: "{{ assets_task_file }}"
  when:
    - assets_task_file is defined
    - "'_assets/tasks/main.yml' in item"

- name: download_assets | Convert Jinja2 templates to JSON - _assets/task/main.yml
  ansible.builtin.set_fact:
    json_data: "{{ lookup('template', item.item) | from_yaml | default({}) }}"
  loop: "{{ template_files_stats_is.results }}"
  register: json_data_results_image_sets
  when: item.stat is defined and item.stat.exists

- name: download_assets | Combine JSON data - asset_role_list
  ansible.builtin.set_fact:
    asset_role_list: >-
      {{
        asset_role_list | default([]) + [json_data_results_image_sets.results[idx].item.item]
      }}
  loop: "{{ range(0, json_data_results_image_sets.results | length) }}"
  loop_control:
    index_var: idx
  when: json_data_results_image_sets.results[idx].ansible_facts.json_data is defined

- name: download_assets | Combine JSON data - asset_role_list
  ansible.builtin.set_fact:
    asset_role_list: "{{ asset_role_list | default([]) | flatten | unique }}"

- name: download_assets | Get each assets role that exists
  ansible.builtin.set_fact:
    existing_assets_role: >-
      {{
        existing_assets_role | default([]) + [item.split('/')[-6] + '.' + item.split('/')[-5] + '.' + item.split('/')[-3]]
      }}
  loop: "{{ asset_role_list }}"

- name: download_assets | Include asset management roles
  ansible.builtin.include_role:
    name: "{{ item }}"
    public: true
  loop: "{{ existing_assets_role }}"
  when: existing_assets_role is defined
