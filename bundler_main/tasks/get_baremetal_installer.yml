# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        get_baremetal_installer.yml                                       #
# Version:                                                                        #
#               2025-08-05 espy & <PERSON><PERSON>                                    #
#               2025-21-07 espy                                                   #
# Create Date:  2025-21-07                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: get_baremetal_installer | Get installer
  ansible.builtin.get_url:
    url: "{{ installer_url }}"
    dest: /tmp/openshift-install-linux.tar.gz
    mode: "0644"
    username: "{{ bundler.nexus_user }}"
    password: "{{ bundler.nexus_password }}"
    timeout: 600

- name: get_baremetal_installer | Extract installer
  ansible.builtin.unarchive:
    src: /tmp/openshift-install-linux.tar.gz
    dest: /tmp
    remote_src: true

- name: get_baremetal_installer | Copying Files
  when: bundler.distro == "sno"
  block:
    - name: get_baremetal_installer | Copy installer to nexus directory
      ansible.builtin.copy:
        src: /tmp/{{ install_file_name }}
        dest: "{{ package_files }}/"
        mode: "0755"
        remote_src: true

    - name: get_baremetal_installer | Create a symlink to the openshift baremetal install
      ansible.builtin.file:
        src: "{{ package_files }}/{{ bundler.openshift_install_file }}"
        dest: ~/.local/bin/{{ bundler.openshift_install_file }}
        state: link

- name: get_baremetal_installer | Not SNO
  when: bundler.distro != "sno"
  block:
    - name: get_baremetal_installer | Copy installer to nexus directory
      ansible.builtin.copy:
        src: /tmp/{{ install_file_name }}
        dest: "{{ galaxy_nexus_repo_files_dir }}/{{ bundler.openshift_install_file }}"
        mode: "0755"
        remote_src: true

    - name: get_baremetal_installer | Create a symlink to the openshift baremetal install
      ansible.builtin.file:
        src: "{{ galaxy_nexus_repo_files_dir }}/{{ bundler.openshift_install_file }}"
        dest: ~/.local/bin/{{ bundler.openshift_install_file }}
        state: link
