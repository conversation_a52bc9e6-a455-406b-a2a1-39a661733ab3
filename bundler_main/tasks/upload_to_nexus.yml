# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        upload_to_nexus.yml                                               #
# Version:                                                                        #
#               2025-21-07 espy                                                   #
# Create Date:  2025-21-07                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: upload_to_nexus | Get all files
  ansible.builtin.find:
    paths:
      - "{{ galaxy_nexus_repo_files_dir }}"
    file_type: file
    recurse: true
  register: files_to_upload

- name: upload_to_nexus | Upload all files in {{ galaxy_nexus_repo_files_dir + ' folder to nexus' }}
  ansible.builtin.uri:
    url: "{{ default_nexus_url }}/repository/{{ bundler.galaxy_nexus_repo_name }}/{{ item.path | regex_replace('^' + galaxy_nexus_repo_files_dir + '/', '') }}"
    method: PUT
    user: "{{ bundler.galaxy_nexus_user }}"
    password: "{{ bundler.galaxy_nexus_password }}"
    headers:
      Content-Type: application/x-tar
    src: "{{ item.path }}"
    force_basic_auth: true
    remote_src: true
    status_code:
      - 201
  loop: "{{ files_to_upload.files }}"
  loop_control:
    label: "{{ item.path | regex_replace('^' + galaxy_nexus_repo_files_dir + '/', '') }}"
  async: 1800
  poll: 0
  register: file_upload_jobs

- name: upload_to_nexus | Wait for all files to be uploaded
  ansible.builtin.async_status:
    jid: "{{ result.ansible_job_id }}"
  loop: "{{ file_upload_jobs.results }}"
  loop_control:
    loop_var: result
    label: "{{ result.item.path }}"
  register: async_poll_results
  until: async_poll_results.finished
  retries: 60
  delay: 30

- name: upload_to_nexus | Relieve some more space
  ansible.builtin.file:
    state: absent
    path: "{{ galaxy_nexus_repo_files_dir }}"
  when: not bundler.developer_run
