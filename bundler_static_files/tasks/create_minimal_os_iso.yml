# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        create_minimal_os_iso.yml                                         #
# Version:                                                                        #
#               2025-21-07 espy                                                   #
# Create Date:  2025-21-07                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: create_minimal_os_iso | Delete previous modified iso folder
  ansible.builtin.file:
    path: "{{ modified_iso_location }}"
    state: absent

- name: create_minimal_os_iso | Create new modified iso folder
  ansible.builtin.file:
    path: "{{ modified_iso_location }}"
    state: directory
    mode: "0755"

- name: create_minimal_os_iso | Download rhel iso nexus
  ansible.builtin.get_url:
    url: https://{{ bundler.nexus_domain }}/{{ bundler.nexus_ISO_path }}/{{ bundler.rhel_iso_name }}
    username: "{{ bundler.nexus_user }}"
    password: "{{ bundler.nexus_password }}"
    dest: "{{ original_rhel_iso_location }}"
    mode: "0644"
    timeout: 600
  when: storage_backend == 'nexus'

- name: create_minimal_os_iso | Download rhel iso s3
  amazon.aws.s3_object:
    aws_ca_bundle: "{{ bundler.galaxy_ca_cert }}"
    bucket: "{{ bundler.aws_s3_bucket }}"
    access_key: "{{ bundler.aws_access_key_id }}"
    secret_key: "{{ bundler.aws_secret_access_key }}"
    region: "{{ bundler.aws_default_region }}"
    endpoint_url: "{{ bundler.aws_s3_endpoint_url }}"
    object: "galaxy/{{ galaxy.version }}/{{ bundler.rhel_iso_name }}"
    dest: "{{ original_rhel_iso_location }}"
    mode: get
    overwrite: latest
  when: storage_backend == 's3'

- name: create_minimal_os_iso | Unarchive iso files to modified iso directory
  ansible.builtin.command:
    cmd: bsdtar -xf {{ original_rhel_iso_location }} -C {{ modified_iso_location }}/
  changed_when: true

- name: create_minimal_os_iso | Add write access for owner of all files in modified iso
  ansible.builtin.file:
    path: "{{ modified_iso_location }}"
    mode: u+w
    recurse: true

# DO NOT DELETE This is the command to get the packages dnf download --urls --quiet $(dnf repoquery --installed --quiet) | awk -F/ '{print $NF}'
- name: create_minimal_os_iso | Copy bare packages file
  ansible.builtin.copy:
    src: bare_os_install_packages-{{ bundler.rhel_major_version }}.{{ bundler.rhel_minor_version }}.txt
    dest: "{{ bare_packages_file }}"
    mode: "0644"

- name: create_minimal_os_iso | Display list of packages
  ansible.builtin.debug:
    msg: "{{ lookup('file', bare_packages_file) }}"

- name: create_minimal_os_iso | Display list of deleted packages Appstream
  ansible.builtin.debug:
    msg: "{{ lookup('pipe', 'find ' + modified_iso_location + '/AppStream/Packages -type f | grep -vf ' + bare_packages_file) }}"

- name: create_minimal_os_iso | Delete unnecessary packages from iso AppStream
  ansible.builtin.shell:
    cmd: set -o pipefail && find {{ modified_iso_location }}/AppStream/Packages -type f | grep -vf {{ bare_packages_file }} | xargs rm -f
  changed_when: true

- name: create_minimal_os_iso | Display list of deleted packages BaseOS
  ansible.builtin.debug:
    msg: "{{ lookup('pipe', 'find ' + modified_iso_location + '/BaseOS/Packages -type f | grep -vf ' + bare_packages_file) }}"

- name: create_minimal_os_iso | Delete unnecessary packages from iso BaseOS
  ansible.builtin.shell:
    cmd: set -o pipefail && find {{ modified_iso_location }}/BaseOS/Packages -type f | grep -vf {{ bare_packages_file }} | xargs rm -f
  changed_when: true

- name: create_minimal_os_iso | Make iso
  ansible.builtin.shell:
    cmd: |
      set -e
      mkisofs \
        -o {{ output_iso }} \
        -b isolinux/isolinux.bin \
        -J -R -l \
        -c isolinux/boot.cat -no-emul-boot \
        -boot-load-size 4 \
        -boot-info-table \
        -eltorito-alt-boot \
        -e images/efiboot.img -no-emul-boot \
        -graft-points \
        -joliet-long -V "RHEL-{{ bundler.rhel_major_version }}-{{ bundler.rhel_minor_version }}-{{ bundler.rhel_patch_version }}-BaseOS-x86_64" \
        {{ modified_iso_location }}/
      isohybrid --uefi {{ output_iso }}
      implantisomd5 {{ output_iso }}
      chmod 644 {{ output_iso }}
  vars:
    output_iso: "{{ tmp_uploads }}/ocp-{{ bundler.rhel_iso_name }}"
  changed_when: true
