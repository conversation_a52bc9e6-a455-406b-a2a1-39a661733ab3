# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        lmco_galaxy.yml                                                   #
# Version:                                                                        #
#               2025-08-05 <PERSON>osn, Kobby & Espy                                   #
#               2025-21-07 espy                                                   #
# Create Date:  2025-21-07                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

default_bundler:
  http_proxy: http://proxy-zsgov.external.lmco.com:80
  https_proxy: http://proxy-zsgov.external.lmco.com:80
  standard_no_proxy_addresses:
    - .lmco.com
    - localhost
    - 127.0.0.1
  nexus_user: "{{ lookup('env', 'NEXUS_USER') }}"
  nexus_password: "{{ lookup('env', 'NEXUS_PASSWORD') }}"
  nexus_domain: "{{ lookup('env', 'NEXUS_URL') }}"
  nexus_repo_path: repository/Galaxy/galaxy_files
  nexus_bundle_path: repository/space-station/bundles
  nexus_ISO_path: repository/Galaxy/ISOs
  nexus_image_version: 3.70.1
  redhat_registry_pull_secret_file:

  # AWS Settings
  aws_access_key_id: ""
  aws_secret_access_key: ""
  aws_default_region: ""
  aws_s3_bucket: "lm-galaxy"
  aws_s3_endpoint_url: "https://s3.us-east-1.amazonaws.com"

  # Galaxy Bundle Vars
  additional_files: ""
  business_area: ""
  bundle_s3_folder_name: "disconnected_bundle"
  program: ""
  openshift_version: "{{ galaxy.ocp.version }}"
  okd_version: 4.15.0-0.okd-2024-03-10-010116
  # okd_version: 4.17.0-okd-scos.0
  distro: >-
    {{
      lmco_openshift_products is defined
      and lmco_openshift_products | selectattr('name', 'equalto', 'galaxy_platform') | map(attribute='provider') | first is defined
      and (
        lmco_openshift_products | selectattr('name', 'equalto', 'galaxy_platform') | map(attribute='provider') | first == 'redhat_openshift'
      ) and 'ocp'
      or
      lmco_openshift_products is defined
      and lmco_openshift_products | selectattr('name', 'equalto', 'galaxy_platform') | map(attribute='provider') | first is defined
      and (
        lmco_openshift_products | selectattr('name', 'equalto', 'galaxy_platform') | map(attribute='provider') | first == 'okd'
      ) and 'okd'
      or
      lmco_openshift_products is defined
      and lmco_openshift_products | selectattr('name', 'equalto', 'galaxy_platform') | map(attribute='provider') | first is defined
      and (
        lmco_openshift_products | selectattr('name', 'equalto', 'galaxy_platform') | map(attribute='provider') | first == 'sno'
      ) and 'sno'
      or
      'ocp'
    }}

  ocp_channels:
    - name: stable-4.15
      type: ocp
      minVersion: '4.14.31'
      maxVersion: '4.14.31'
    - name: stable-4.15
      type: ocp
      minVersion: '4.15.51'
      maxVersion: '4.15.51'
    - name: stable-4.16
      type: ocp
      minVersion: '4.16.41'
      maxVersion: '4.16.41'
    - name: stable-4.17
      type: ocp
      minVersion: '4.17.32'
      maxVersion: '4.17.32'

  galaxy_nexus_user: "admin"
  galaxy_nexus_password: "{{ galaxy.nexus.password }}"
  rhel_major_version: "{{ galaxy.rhel.major_version }}"
  rhel_minor_version: "{{ galaxy.rhel.minor_version }}"
  rhel_patch_version: "{{ galaxy.rhel.patch_version }}"

  # Names and locations
  ca_cert: /etc/pki/ca-trust/extracted/pem/tls-ca-bundle.pem
  galaxy_ca_cert: /etc/pki/ca-trust/extracted/pem/tls-ca-bundle.pem
  openshift_install_file: "{{ galaxy.ocp.install_file_name }}"
  apps_install_path: "/opt/openshift"
  default_registry_url: "{{ galaxy.registry.initial_hostname }}"
  default_nexus_url: nexus.galaxy.lmco.com
  galaxy_nexus_repo_name: "{{ galaxy.nexus.repo_name }}"
  verson_vars_file: "{{ galaxy.verson_vars_file_name }}"
  bastion_rhel_packages_tarball: "{{ galaxy.rhel.packages_file_name }}"
  nexus_http_port: 8081
  registry_port: 8082
  nexus_image_file: nexus.tar
  nexus_storage: "{{ galaxy.nexus.storage_file_name }}"
  platform: >-
    {{
      lmco_openshift_products is defined
      and lmco_openshift_products | selectattr('name', 'equalto', 'galaxy_platform') | map(attribute='target') | first is defined
      and (
        lmco_openshift_products | selectattr('name', 'equalto', 'galaxy_platform') | map(attribute='target') | first == 'aws'
      ) and 'aws'
      or
      lmco_openshift_products is defined
      and lmco_openshift_products | selectattr('name', 'equalto', 'galaxy_platform') | map(attribute='target') | first is defined
      and 'baremetal'
      or
      'baremetal'
    }}

  # Flags
  validate_certs: true
  developer_run: false
  dnf_isos:
    - iso: rhel-8.6-x86_64-dvd.iso
      name: RHEL8
      sdx_file: rhel-8.6.0.json.bz2
      repos:
        - AppStream
        - BaseOS
      version: 8.6.0
    - iso: rhel-9.5-x86_64-dvd.iso
      name: RHEL9
      sdx_file: rhel-9.5.json.bz2
      repos:
        - AppStream
        - BaseOS
      version: 9.5.0
    - iso: Satellite-6.16.0-rhel-9-x86_64.dvd.iso
      name: Satellite
      sdx_file: rhn_satellite_6.16.json.bz2
      repos:
        - Satellite
      version: 6.16.0

bundler_base: "{{ default_bundler | default({}) |
  combine(galaxy_bundler | default({}), recursive=true) |
  combine(lmco_bundler | default({}), recursive=true) |
  combine(cli_bundler | default({}), recursive=true) }}"

default_derived_bundler:
  ocp_rhel_iso_name: "ocp-rhel-{{ bundler_base.rhel_major_version }}.{{ bundler_base.rhel_minor_version }}-x86_64-dvd.iso"
  rhel_iso_name: "rhel-{{ bundler_base.rhel_major_version }}.{{ bundler_base.rhel_minor_version }}-x86_64-dvd.iso"

derived_bundler: "{{ bundler_base | default({}) | combine(default_derived_bundler | default({}), recursive=true) }}"
